package com.geeksec.alarm.notification.source;

import java.util.List;
import java.util.Queue;
import java.util.LinkedList;
import java.util.concurrent.CompletableFuture;

import org.apache.flink.api.connector.source.ReaderOutput;
import org.apache.flink.api.connector.source.SourceReader;
import org.apache.flink.api.connector.source.SourceReaderContext;
import org.apache.flink.core.io.InputStatus;

import com.geeksec.alarm.notification.client.AlarmServiceClient;
import com.geeksec.alarm.notification.model.NotificationSubscription;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置数据源读取器
 * 负责实际读取订阅配置数据
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSourceReader implements SourceReader<NotificationSubscription, SubscriptionConfigSplit> {
    
    private final AlarmServiceClient alarmServiceClient;
    private final SourceReaderContext readerContext;
    private final Queue<NotificationSubscription> pendingSubscriptions;
    
    private boolean finished = false;
    private boolean dataLoaded = false;
    
    public SubscriptionConfigSourceReader(AlarmServiceClient alarmServiceClient, SourceReaderContext readerContext) {
        this.alarmServiceClient = alarmServiceClient;
        this.readerContext = readerContext;
        this.pendingSubscriptions = new LinkedList<>();
    }
    
    @Override
    public void start() {
        log.info("启动订阅配置数据源读取器");
    }
    
    @Override
    public InputStatus pollNext(ReaderOutput<NotificationSubscription> output) throws Exception {
        // 如果还没有加载数据，先加载
        if (!dataLoaded) {
            loadSubscriptionData();
            dataLoaded = true;
        }
        
        // 输出待处理的订阅配置
        if (!pendingSubscriptions.isEmpty()) {
            NotificationSubscription subscription = pendingSubscriptions.poll();
            output.collect(subscription);
            log.debug("输出订阅配置: {}", subscription.getSubscriptionId());
            return InputStatus.MORE_AVAILABLE;
        }
        
        // 如果没有更多数据，标记为完成
        if (!finished) {
            finished = true;
            log.info("订阅配置数据读取完成");
        }
        
        return InputStatus.END_OF_INPUT;
    }
    
    @Override
    public List<SubscriptionConfigSplit> snapshotState(long checkpointId) {
        log.debug("创建检查点快照: checkpointId={}", checkpointId);
        // 对于这个简单的数据源，我们不需要保存状态
        return List.of();
    }
    
    @Override
    public CompletableFuture<Void> isAvailable() {
        // 如果有待处理的数据或者还没有加载数据，立即可用
        if (!pendingSubscriptions.isEmpty() || !dataLoaded) {
            return CompletableFuture.completedFuture(null);
        }
        
        // 否则，数据已经读取完成
        return CompletableFuture.completedFuture(null);
    }
    
    @Override
    public void addSplits(List<SubscriptionConfigSplit> splits) {
        log.info("添加分片: {}", splits.size());
        // 对于这个简单的数据源，我们不需要处理分片
        // 只是触发数据加载
        if (!dataLoaded) {
            try {
                loadSubscriptionData();
                dataLoaded = true;
            } catch (Exception e) {
                log.error("加载订阅配置数据失败", e);
            }
        }
    }
    
    @Override
    public void notifyNoMoreSplits() {
        log.info("通知没有更多分片");
        // 标记为完成
        finished = true;
    }
    
    @Override
    public void close() throws Exception {
        log.info("关闭订阅配置数据源读取器");
        pendingSubscriptions.clear();
    }
    
    /**
     * 加载订阅配置数据
     */
    private void loadSubscriptionData() throws Exception {
        log.info("开始加载订阅配置数据");
        
        try {
            List<NotificationSubscription> subscriptions = alarmServiceClient.getAllActiveSubscriptions();
            log.info("成功获取到 {} 个活动订阅配置", subscriptions.size());
            
            // 将订阅配置添加到待处理队列
            pendingSubscriptions.addAll(subscriptions);
            
            log.info("订阅配置数据加载完成，共 {} 个配置", subscriptions.size());
            
        } catch (Exception e) {
            log.error("获取订阅配置失败", e);
            throw e;
        }
    }
}
