package com.geeksec.alarm.notification.source;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.apache.flink.core.io.SimpleVersionedSerializer;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置分片序列化器
 * 用于检查点和恢复
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SubscriptionConfigSplitSerializer implements SimpleVersionedSerializer<SubscriptionConfigSplit> {
    
    private static final int VERSION = 1;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public int getVersion() {
        return VERSION;
    }
    
    @Override
    public byte[] serialize(SubscriptionConfigSplit split) throws IOException {
        try {
            String json = objectMapper.writeValueAsString(split);
            return json.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("序列化订阅配置分片失败: splitId={}", split.getSplitId(), e);
            throw new IOException("序列化订阅配置分片失败", e);
        }
    }
    
    @Override
    public SubscriptionConfigSplit deserialize(int version, byte[] serialized) throws IOException {
        if (version != VERSION) {
            throw new IOException("不支持的版本: " + version);
        }
        
        try {
            String json = new String(serialized, StandardCharsets.UTF_8);
            return objectMapper.readValue(json, SubscriptionConfigSplit.class);
        } catch (Exception e) {
            log.error("反序列化订阅配置分片失败", e);
            throw new IOException("反序列化订阅配置分片失败", e);
        }
    }
}
