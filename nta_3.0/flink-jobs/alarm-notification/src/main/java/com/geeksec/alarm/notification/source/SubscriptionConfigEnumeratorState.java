package com.geeksec.alarm.notification.source;

import java.io.Serializable;
import java.util.Set;
import java.util.HashSet;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 订阅配置数据源枚举器状态
 * 用于检查点和恢复
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionConfigEnumeratorState implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 已分配的分片ID集合
     */
    private Set<String> assignedSplitIds;
    
    /**
     * 是否已完成初始化
     */
    private boolean initialized;
    
    /**
     * 创建默认状态
     */
    public static SubscriptionConfigEnumeratorState createDefault() {
        return new SubscriptionConfigEnumeratorState(new HashSet<>(), false);
    }
    
    /**
     * 添加已分配的分片ID
     */
    public void addAssignedSplitId(String splitId) {
        if (assignedSplitIds == null) {
            assignedSplitIds = new HashSet<>();
        }
        assignedSplitIds.add(splitId);
    }
    
    /**
     * 检查分片是否已分配
     */
    public boolean isSplitAssigned(String splitId) {
        return assignedSplitIds != null && assignedSplitIds.contains(splitId);
    }
    
    /**
     * 标记为已初始化
     */
    public void markInitialized() {
        this.initialized = true;
    }
}
