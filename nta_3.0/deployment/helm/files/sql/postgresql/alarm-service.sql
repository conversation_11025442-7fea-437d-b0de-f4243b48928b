
CREATE TABLE nta.alarm_base (
                                   id varchar NULL, -- id
                                   task_id int8 NULL, -- 任务id
                                   "time" timestamp NULL, -- 告警时间
                                   alarm_name varchar NULL, -- 告警名称
                                   targets _json NULL, -- 告警对象
                                   victim _json NULL, -- 受害者
                                   attacker _json NULL, -- 攻击者
                                   attack_level int4 NULL, -- 威胁权重
                                   alarm_status int4 NULL, -- 处理状态
                                   "json" jsonb NULL, -- 告警具体数据
                                   alarm_knowledge_id int8 NULL, -- 告警名称id
                                   alarm_type varchar NULL, -- 告警类型
                                   attack_chain_name _varchar NULL -- 攻击链名称
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN nta.alarm_base.id IS 'id';
COMMENT ON COLUMN nta.alarm_base.task_id IS '任务id';
COMMENT ON COLUMN nta.alarm_base."time" IS '告警时间';
COMMENT ON COLUMN nta.alarm_base.alarm_name IS '告警名称';
COMMENT ON COLUMN nta.alarm_base.targets IS '告警对象';
COMMENT ON COLUMN nta.alarm_base.victim IS '受害者';
COMMENT ON COLUMN nta.alarm_base.attacker IS '攻击者';
COMMENT ON COLUMN nta.alarm_base.attack_level IS '威胁权重';
COMMENT ON COLUMN nta.alarm_base.alarm_status IS '处理状态';
COMMENT ON COLUMN nta.alarm_base."json" IS '告警具体数据';
COMMENT ON COLUMN nta.alarm_base.alarm_knowledge_id IS '告警名称id';
COMMENT ON COLUMN nta.alarm_base.alarm_type IS '告警类型';
COMMENT ON COLUMN nta.alarm_base.attack_chain_name IS '攻击链名称';