-- ========================================
-- NTA 3.0 告警管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 告警管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- 枚举类型定义
DO $$ BEGIN
    CREATE TYPE cyber_kill_chain_enum AS ENUM (
        'UNKNOWN',
        'RECONNAISSANCE',
        'WEAPONIZATION', 
        'DELIVERY',
        'EXPLOITATION',
        'INSTALLATION',
        'COMMAND_AND_CONTROL',
        'ACTIONS_ON_OBJECTIVES',
        'LATERAL_MOVEMENT',
        'PERSISTENCE',
        'PRIVILEGE_ESCALATION',
        'DEFENSE_EVASION',
        'CREDENTIAL_ACCESS',
        'DISCOVERY',
        'COLLECTION',
        'EXFILTRATION',
        'IMPACT'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE threat_level_enum AS ENUM (
        'LOW',
        'MEDIUM', 
        'HIGH',
        'CRITICAL'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ========================================
-- 告警核心业务表
-- ========================================

-- 告警主表（对应Alarm聚合根）
DROP TABLE IF EXISTS alarm CASCADE;

CREATE TABLE alarm (
    id VARCHAR(32) PRIMARY KEY,
    alarm_index VARCHAR(100),
    type VARCHAR(50),
    version INTEGER DEFAULT 1,
    score INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 0,
    handler VARCHAR(100),
    handle_time TIMESTAMP,
    handle_note TEXT
);

COMMENT ON TABLE alarm IS '告警主表';
COMMENT ON COLUMN alarm.id IS '告警ID';
COMMENT ON COLUMN alarm.alarm_index IS '告警索引';
COMMENT ON COLUMN alarm.type IS '告警类型';
COMMENT ON COLUMN alarm.version IS '告警版本';
COMMENT ON COLUMN alarm.score IS '告警分数';
COMMENT ON COLUMN alarm.create_time IS '创建时间';
COMMENT ON COLUMN alarm.status IS '处理状态：0-未处理，1-已处理，2-已忽略';
COMMENT ON COLUMN alarm.handler IS '处理人';
COMMENT ON COLUMN alarm.handle_time IS '处理时间';
COMMENT ON COLUMN alarm.handle_note IS '处理备注';

-- 创建索引
CREATE INDEX idx_alarm_index ON alarm (alarm_index);
CREATE INDEX idx_alarm_type ON alarm (type);
CREATE INDEX idx_alarm_status ON alarm (status);
CREATE INDEX idx_alarm_create_time ON alarm (create_time);

-- 告警源信息表（对应AlarmSource实体）
DROP TABLE IF EXISTS alarm_source CASCADE;

CREATE TABLE alarm_source (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    alarm_status INTEGER DEFAULT 0,
    alarm_type VARCHAR(100),
    attack_chain_name VARCHAR(200),
    attack_family VARCHAR(100),
    ioc TEXT,
    alarm_knowledge_id BIGINT,
    attack_level INTEGER DEFAULT 0,
    alarm_name VARCHAR(200),
    alarm_principle TEXT,
    time BIGINT,
    attack_route TEXT,
    alarm_handle_method VARCHAR(500),
    alarm_related_label TEXT,
    alarm_session_list TEXT,
    attack_chain_list TEXT,
    task_id INTEGER,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_source IS '告警源信息表';
COMMENT ON COLUMN alarm_source.id IS '源信息ID';
COMMENT ON COLUMN alarm_source.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_source.alarm_status IS '告警状态';
COMMENT ON COLUMN alarm_source.alarm_type IS '告警类型';
COMMENT ON COLUMN alarm_source.attack_chain_name IS '攻击链名称';
COMMENT ON COLUMN alarm_source.attack_family IS '攻击家族';
COMMENT ON COLUMN alarm_source.ioc IS 'IOC信息';
COMMENT ON COLUMN alarm_source.alarm_knowledge_id IS '告警知识ID';
COMMENT ON COLUMN alarm_source.attack_level IS '攻击等级';
COMMENT ON COLUMN alarm_source.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_source.alarm_principle IS '告警原理';
COMMENT ON COLUMN alarm_source.time IS '告警时间戳';
COMMENT ON COLUMN alarm_source.attack_route IS '攻击路径';
COMMENT ON COLUMN alarm_source.alarm_handle_method IS '告警处理方法';
COMMENT ON COLUMN alarm_source.alarm_related_label IS '告警关联标签';
COMMENT ON COLUMN alarm_source.alarm_session_list IS '告警会话列表';
COMMENT ON COLUMN alarm_source.attack_chain_list IS '攻击链列表';
COMMENT ON COLUMN alarm_source.task_id IS '任务ID';

-- 创建索引
CREATE INDEX idx_alarm_source_alarm_id ON alarm_source (alarm_id);
CREATE INDEX idx_alarm_source_alarm_type ON alarm_source (alarm_type);
CREATE INDEX idx_alarm_source_task_id ON alarm_source (task_id);
CREATE INDEX idx_alarm_source_time ON alarm_source (time);
CREATE INDEX idx_alarm_source_knowledge_id ON alarm_source (alarm_knowledge_id);

-- 告警攻击者表（对应AlarmAttacker实体）
DROP TABLE IF EXISTS alarm_attacker CASCADE;

CREATE TABLE alarm_attacker (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    ip VARCHAR(45),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_attacker IS '告警攻击者表';
COMMENT ON COLUMN alarm_attacker.id IS '攻击者ID';
COMMENT ON COLUMN alarm_attacker.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_attacker.ip IS '攻击者IP地址';
COMMENT ON COLUMN alarm_attacker.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_alarm_attacker_alarm_id ON alarm_attacker (alarm_id);
CREATE INDEX idx_alarm_attacker_ip ON alarm_attacker (ip);

-- 告警受害者表（对应AlarmVictim实体）
DROP TABLE IF EXISTS alarm_victim CASCADE;

CREATE TABLE alarm_victim (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    ip VARCHAR(45),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_victim IS '告警受害者表';
COMMENT ON COLUMN alarm_victim.id IS '受害者ID';
COMMENT ON COLUMN alarm_victim.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_victim.ip IS '受害者IP地址';
COMMENT ON COLUMN alarm_victim.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_alarm_victim_alarm_id ON alarm_victim (alarm_id);
CREATE INDEX idx_alarm_victim_ip ON alarm_victim (ip);

-- 告警目标表（对应AlarmTargets实体）
DROP TABLE IF EXISTS alarm_targets CASCADE;

CREATE TABLE alarm_targets (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    name VARCHAR(255),
    type VARCHAR(45),
    labels TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_targets IS '告警目标表';
COMMENT ON COLUMN alarm_targets.id IS '目标ID';
COMMENT ON COLUMN alarm_targets.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_targets.name IS '目标名称';
COMMENT ON COLUMN alarm_targets.type IS '目标类型';
COMMENT ON COLUMN alarm_targets.labels IS '标签信息';
COMMENT ON COLUMN alarm_targets.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_alarm_targets_alarm_id ON alarm_targets (alarm_id);
CREATE INDEX idx_alarm_targets_type ON alarm_targets (type);

-- 告警原因表（对应AlarmReason实体）
DROP TABLE IF EXISTS alarm_reason CASCADE;

CREATE TABLE alarm_reason (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    key VARCHAR(100),
    actual_value TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_reason IS '告警原因表';
COMMENT ON COLUMN alarm_reason.id IS '原因ID';
COMMENT ON COLUMN alarm_reason.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_reason.key IS '原因键';
COMMENT ON COLUMN alarm_reason.actual_value IS '实际值';
COMMENT ON COLUMN alarm_reason.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_alarm_reason_alarm_id ON alarm_reason (alarm_id);
CREATE INDEX idx_alarm_reason_key ON alarm_reason (key);

-- ========================================
-- 告警配置和管理表
-- ========================================

-- 告警类型配置表（对应AlarmType实体）
DROP TABLE IF EXISTS alarm_type_config CASCADE;

CREATE TABLE alarm_type_config (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    default_threat_level VARCHAR(20) DEFAULT 'MEDIUM',
    enabled BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_type_config IS '告警类型配置表（系统配置用）';
COMMENT ON COLUMN alarm_type_config.type_code IS '告警类型代码，唯一标识';
COMMENT ON COLUMN alarm_type_config.type_name IS '告警类型名称';
COMMENT ON COLUMN alarm_type_config.description IS '告警类型描述';
COMMENT ON COLUMN alarm_type_config.category IS '告警类型分类';
COMMENT ON COLUMN alarm_type_config.default_threat_level IS '默认威胁等级: LOW, MEDIUM, HIGH, CRITICAL';
COMMENT ON COLUMN alarm_type_config.enabled IS '是否启用';
COMMENT ON COLUMN alarm_type_config.sort_order IS '排序序号';

-- 创建索引
CREATE INDEX idx_alarm_type_config_code ON alarm_type_config (type_code);
CREATE INDEX idx_alarm_type_config_category ON alarm_type_config (category);
CREATE INDEX idx_alarm_type_config_enabled ON alarm_type_config (enabled);

-- 告警规则表
DROP TABLE IF EXISTS alarm_rules CASCADE;

CREATE TABLE alarm_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    condition_expression TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'MEDIUM',
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_rules IS '告警规则表';
COMMENT ON COLUMN alarm_rules.rule_name IS '规则名称';
COMMENT ON COLUMN alarm_rules.rule_type IS '规则类型';
COMMENT ON COLUMN alarm_rules.condition_expression IS '条件表达式';
COMMENT ON COLUMN alarm_rules.severity IS '严重程度: LOW, MEDIUM, HIGH, CRITICAL';

-- 告警记录表（业务主存储）
DROP TABLE IF EXISTS alarm_records CASCADE;

CREATE TABLE alarm_records (
    id SERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES alarm_rules(id),
    alarm_title VARCHAR(255) NOT NULL,
    alarm_content TEXT,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'OPEN',
    source_data JSONB,
    triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    resolved_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 扩展字段，用于分析
    alarm_type VARCHAR(100),
    attack_stage VARCHAR(100),
    threat_type VARCHAR(100),
    confidence DOUBLE PRECISION,

    -- 网络相关信息
    src_ip INET,
    src_port INTEGER,
    dst_ip INET,
    dst_port INTEGER,
    protocol VARCHAR(50),

    -- 关联信息
    session_id VARCHAR(255),
    task_id VARCHAR(100),
    data_source VARCHAR(100)
);

COMMENT ON TABLE alarm_records IS '告警记录表（业务主存储）';
COMMENT ON COLUMN alarm_records.rule_id IS '关联的告警规则ID';
COMMENT ON COLUMN alarm_records.alarm_title IS '告警标题';
COMMENT ON COLUMN alarm_records.alarm_content IS '告警详细内容';
COMMENT ON COLUMN alarm_records.severity IS '严重程度: LOW, MEDIUM, HIGH, CRITICAL';
COMMENT ON COLUMN alarm_records.status IS '告警状态: OPEN, ACKNOWLEDGED, RESOLVED, FALSE_POSITIVE';
COMMENT ON COLUMN alarm_records.source_data IS '源数据信息 (JSON格式)';
COMMENT ON COLUMN alarm_records.triggered_at IS '告警触发时间';
COMMENT ON COLUMN alarm_records.resolved_at IS '告警解决时间';
COMMENT ON COLUMN alarm_records.resolved_by IS '解决人员';

-- 创建索引以支持高效查询和CDC
CREATE INDEX idx_alarm_records_rule_id ON alarm_records (rule_id);
CREATE INDEX idx_alarm_records_status ON alarm_records (status);
CREATE INDEX idx_alarm_records_severity ON alarm_records (severity);
CREATE INDEX idx_alarm_records_triggered_at ON alarm_records (triggered_at);
CREATE INDEX idx_alarm_records_src_ip ON alarm_records (src_ip);
CREATE INDEX idx_alarm_records_dst_ip ON alarm_records (dst_ip);
CREATE INDEX idx_alarm_records_task_id ON alarm_records (task_id);

-- ========================================
-- 告警订阅和通知表
-- ========================================

-- 告警订阅表
DROP TABLE IF EXISTS alarm_subscription CASCADE;

CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    subscription_name VARCHAR(100) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    priority_level INT DEFAULT 1,

    -- 匹配规则 (JSON格式存储)
    match_rules JSONB,

    -- 通知设置
    notification_channels JSONB,
    frequency_type VARCHAR(20) DEFAULT 'REAL_TIME',
    frequency_config JSONB,

    -- 免打扰设置
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_config JSONB,

    -- 统计信息
    trigger_count INT DEFAULT 0,
    last_triggered_time TIMESTAMP,

    -- 审计字段
    created_by VARCHAR(32),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_subscription IS '告警订阅规则表';
COMMENT ON COLUMN alarm_subscription.user_id IS '用户ID';
COMMENT ON COLUMN alarm_subscription.subscription_name IS '订阅名称';
COMMENT ON COLUMN alarm_subscription.match_rules IS '匹配规则配置(JSON)';
COMMENT ON COLUMN alarm_subscription.notification_channels IS '通知渠道配置(JSON)';
COMMENT ON COLUMN alarm_subscription.frequency_type IS '通知频率类型';

-- 创建索引
CREATE INDEX idx_alarm_subscription_user_id ON alarm_subscription (user_id);
CREATE INDEX idx_alarm_subscription_enabled ON alarm_subscription (enabled);

-- ========================================
-- 告警辅助和配置表
-- ========================================

-- 告警抑制规则表（对应AlarmSuppression实体）
DROP TABLE IF EXISTS alarm_suppression CASCADE;

CREATE TABLE alarm_suppression (
    id SERIAL PRIMARY KEY,
    victim VARCHAR(256),
    attacker VARCHAR(256),
    label VARCHAR(256),
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expire_time TIMESTAMP,
    operator VARCHAR(100),
    created_by VARCHAR(100)
);

COMMENT ON TABLE alarm_suppression IS '告警抑制规则表';
COMMENT ON COLUMN alarm_suppression.id IS '抑制规则ID';
COMMENT ON COLUMN alarm_suppression.victim IS '受害者IP（支持通配符*）';
COMMENT ON COLUMN alarm_suppression.attacker IS '攻击者IP（支持通配符*）';
COMMENT ON COLUMN alarm_suppression.label IS '告警标签（支持通配符*）';
COMMENT ON COLUMN alarm_suppression.description IS '规则描述';
COMMENT ON COLUMN alarm_suppression.enabled IS '是否启用';
COMMENT ON COLUMN alarm_suppression.create_time IS '创建时间';
COMMENT ON COLUMN alarm_suppression.update_time IS '更新时间';
COMMENT ON COLUMN alarm_suppression.expire_time IS '过期时间';
COMMENT ON COLUMN alarm_suppression.operator IS '操作人';
COMMENT ON COLUMN alarm_suppression.created_by IS '创建人';

-- 创建索引
CREATE INDEX idx_alarm_suppression_victim ON alarm_suppression (victim);
CREATE INDEX idx_alarm_suppression_attacker ON alarm_suppression (attacker);
CREATE INDEX idx_alarm_suppression_label ON alarm_suppression (label);
CREATE INDEX idx_alarm_suppression_enabled ON alarm_suppression (enabled);
CREATE INDEX idx_alarm_suppression_expire_time ON alarm_suppression (expire_time);

-- 告警输出配置表
DROP TABLE IF EXISTS alarm_output_config CASCADE;

CREATE TABLE alarm_output_config (
    id SERIAL PRIMARY KEY,
    tool VARCHAR(256),
    status INTEGER,
    ip VARCHAR(256),
    port VARCHAR(256),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_output_config IS '告警输出配置表';

-- 告警扩展目标表
DROP TABLE IF EXISTS alarm_extended_target CASCADE;

CREATE TABLE alarm_extended_target (
    id BIGSERIAL PRIMARY KEY,
    alarm_id INTEGER,
    batch_id INTEGER,
    label_id INTEGER,
    target_ip VARCHAR(256),
    target_port INTEGER,
    target_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_extended_target IS '告警扩展目标表';

-- 创建索引
CREATE INDEX idx_alarm_extended_target_alarm_id ON alarm_extended_target (alarm_id);
CREATE INDEX idx_alarm_extended_target_batch_id ON alarm_extended_target (batch_id);

-- ========================================
-- 告警关联查询支持表
-- ========================================

-- 标签信息表（对应AnalysisLabelInfoEntity实体）
DROP TABLE IF EXISTS tb_tag_info CASCADE;

CREATE TABLE tb_tag_info (
    tag_id SERIAL PRIMARY KEY,
    tag_type INTEGER DEFAULT 1,
    tag_remark TEXT,
    tag_explain VARCHAR(500),
    tag_text VARCHAR(500) NOT NULL,
    tag_num INTEGER DEFAULT 0,
    tag_target_type INTEGER DEFAULT 0,
    default_black_list INTEGER DEFAULT 0,
    default_white_list INTEGER DEFAULT 0,
    black_list INTEGER DEFAULT 0,
    white_list INTEGER DEFAULT 0,
    created_at INTEGER,
    last_created_at INTEGER,
    tag_family INTEGER DEFAULT 0
);

COMMENT ON TABLE tb_tag_info IS '标签信息表';
COMMENT ON COLUMN tb_tag_info.tag_id IS '标签ID';
COMMENT ON COLUMN tb_tag_info.tag_type IS '标签类型：0删除，1生效';
COMMENT ON COLUMN tb_tag_info.tag_remark IS '标签备注';
COMMENT ON COLUMN tb_tag_info.tag_explain IS '标签中文说明';
COMMENT ON COLUMN tb_tag_info.tag_text IS '标签内容';
COMMENT ON COLUMN tb_tag_info.tag_num IS '标签数量';
COMMENT ON COLUMN tb_tag_info.tag_target_type IS '目标类型：0-IP，1-端口，2-应用，3-域名，4-证书，5-MAC，6-连接，7-指纹，9999-所有';
COMMENT ON COLUMN tb_tag_info.default_black_list IS '默认黑名单';
COMMENT ON COLUMN tb_tag_info.default_white_list IS '默认白名单';
COMMENT ON COLUMN tb_tag_info.black_list IS '黑名单';
COMMENT ON COLUMN tb_tag_info.white_list IS '白名单';
COMMENT ON COLUMN tb_tag_info.created_at IS '创建时间';
COMMENT ON COLUMN tb_tag_info.last_created_at IS '最后创建时间';
COMMENT ON COLUMN tb_tag_info.tag_family IS '标签族：0-其他，1-侦察探测，2-武器投递，3-攻击突防，4-命令控制，5-控守操作';

-- 创建索引
CREATE INDEX idx_tb_tag_info_type ON tb_tag_info (tag_type);
CREATE INDEX idx_tb_tag_info_text ON tb_tag_info (tag_text);
CREATE INDEX idx_tb_tag_info_target_type ON tb_tag_info (tag_target_type);
CREATE INDEX idx_tb_tag_info_family ON tb_tag_info (tag_family);

-- 下载任务表（对应DownloadTask实体）
DROP TABLE IF EXISTS tb_download_task CASCADE;

CREATE TABLE tb_download_task (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    path VARCHAR(500),
    query TEXT,
    show_query TEXT,
    type INTEGER DEFAULT 0,
    session_id TEXT,
    state INTEGER DEFAULT 0,
    created_time BIGINT,
    end_time BIGINT,
    status INTEGER DEFAULT 1,
    task_id VARCHAR(500)
);

COMMENT ON TABLE tb_download_task IS '下载任务表';
COMMENT ON COLUMN tb_download_task.id IS '主键';
COMMENT ON COLUMN tb_download_task.user_id IS '创建者用户ID';
COMMENT ON COLUMN tb_download_task.path IS '文件路径';
COMMENT ON COLUMN tb_download_task.query IS 'ES下载检索条件';
COMMENT ON COLUMN tb_download_task.show_query IS '前端展示字段';
COMMENT ON COLUMN tb_download_task.type IS '下载类型：0-部分下载，1-全量下载';
COMMENT ON COLUMN tb_download_task.session_id IS 'session列表信息';
COMMENT ON COLUMN tb_download_task.state IS '任务状态：0-准备数据，1-可下载，2-重新下载，3-已删除，4-待删除';
COMMENT ON COLUMN tb_download_task.created_time IS '创建时间';
COMMENT ON COLUMN tb_download_task.end_time IS '数据存储时间';
COMMENT ON COLUMN tb_download_task.status IS '数据状态：0-删除，1-存在';
COMMENT ON COLUMN tb_download_task.task_id IS '任务ID数组';

-- 创建索引
CREATE INDEX idx_tb_download_task_user_id ON tb_download_task (user_id);
CREATE INDEX idx_tb_download_task_state ON tb_download_task (state);
CREATE INDEX idx_tb_download_task_status ON tb_download_task (status);
CREATE INDEX idx_tb_download_task_created_time ON tb_download_task (created_time);

-- 知识类型视图表（用于告警关联查询）
DROP TABLE IF EXISTS knowledge_type_vo CASCADE;

CREATE TABLE knowledge_type_vo (
    id BIGINT PRIMARY KEY,
    attack_type INTEGER,
    attack_type_name VARCHAR(100),
    alarm_name VARCHAR(200),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE knowledge_type_vo IS '知识类型视图表';
COMMENT ON COLUMN knowledge_type_vo.id IS '知识ID';
COMMENT ON COLUMN knowledge_type_vo.attack_type IS '攻击类型';
COMMENT ON COLUMN knowledge_type_vo.attack_type_name IS '攻击类型名称';
COMMENT ON COLUMN knowledge_type_vo.alarm_name IS '告警名称';
COMMENT ON COLUMN knowledge_type_vo.description IS '描述';

-- 创建索引
CREATE INDEX idx_knowledge_type_vo_attack_type ON knowledge_type_vo (attack_type);

-- 标签表（用于告警关联查询）
DROP TABLE IF EXISTS label CASCADE;

CREATE TABLE label (
    id BIGINT PRIMARY KEY,
    display_name VARCHAR(200),
    label_type VARCHAR(50),
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE label IS '标签表';
COMMENT ON COLUMN label.id IS '标签ID';
COMMENT ON COLUMN label.display_name IS '显示名称';
COMMENT ON COLUMN label.label_type IS '标签类型';
COMMENT ON COLUMN label.description IS '描述';
COMMENT ON COLUMN label.enabled IS '是否启用';

-- 创建索引
CREATE INDEX idx_label_type ON label (label_type);
CREATE INDEX idx_label_enabled ON label (enabled);

-- 特征规则表（用于告警关联查询）
DROP TABLE IF EXISTS feature_rule CASCADE;

CREATE TABLE feature_rule (
    id BIGINT PRIMARY KEY,
    rule_name VARCHAR(200),
    rule_type VARCHAR(50),
    rule_content TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE feature_rule IS '特征规则表';
COMMENT ON COLUMN feature_rule.id IS '规则ID';
COMMENT ON COLUMN feature_rule.rule_name IS '规则名称';
COMMENT ON COLUMN feature_rule.rule_type IS '规则类型';
COMMENT ON COLUMN feature_rule.rule_content IS '规则内容';
COMMENT ON COLUMN feature_rule.enabled IS '是否启用';

-- 创建索引
CREATE INDEX idx_feature_rule_type ON feature_rule (rule_type);
CREATE INDEX idx_feature_rule_enabled ON feature_rule (enabled);

-- ========================================
-- 告警统计表
-- ========================================

-- 告警统计表
DROP TABLE IF EXISTS alarm_statistics CASCADE;

CREATE TABLE alarm_statistics (
    hour_times INTEGER NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum NOT NULL,
    num INTEGER NOT NULL DEFAULT 0,
    tkey BIGINT PRIMARY KEY
);

COMMENT ON TABLE alarm_statistics IS '告警统计表';

-- ========================================
-- 威胁知识告警表（兼容现有系统）
-- ========================================

-- 威胁知识告警表
DROP TABLE IF EXISTS threat_knowledge_alarm CASCADE;

CREATE TABLE threat_knowledge_alarm (
    knowledge_alarm_id INTEGER NOT NULL,
    alarm_name VARCHAR(64) NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'UNKNOWN',
    attack_time VARCHAR(256) DEFAULT '',
    relation_label_id VARCHAR(2048) DEFAULT '',
    exclude_label_id VARCHAR(2048) DEFAULT '',
    black_list INTEGER DEFAULT 0,
    remark TEXT DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_knowledge_alarm IS '威胁知识告警表';

-- 告警类型定义表（兼容现有系统）
DROP TABLE IF EXISTS alarm_types CASCADE;

CREATE TABLE alarm_types (
    id BIGSERIAL PRIMARY KEY,
    knowledge_id BIGINT UNIQUE NOT NULL,
    alarm_name VARCHAR(255) NOT NULL,
    alarm_principle TEXT,
    cyber_kill_chain cyber_kill_chain_enum,
    threat_level threat_level_enum,
    include_labels INTEGER [],
    exclude_labels INTEGER [],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_types IS '告警类型定义表（知识库用）';

-- ========================================
-- 数据库函数和触发器
-- ========================================

-- 更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要自动更新时间戳的表添加触发器
CREATE TRIGGER update_alarm_type_config_updated_at BEFORE UPDATE ON alarm_type_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_rules_updated_at BEFORE UPDATE ON alarm_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_records_updated_at BEFORE UPDATE ON alarm_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_output_config_updated_at BEFORE UPDATE ON alarm_output_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_knowledge_type_vo_updated_at BEFORE UPDATE ON knowledge_type_vo FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_label_updated_at BEFORE UPDATE ON label FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_rule_updated_at BEFORE UPDATE ON feature_rule FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_threat_knowledge_alarm_updated_at BEFORE UPDATE ON threat_knowledge_alarm FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_types_updated_at BEFORE UPDATE ON alarm_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_subscription_updated_time BEFORE UPDATE ON alarm_subscription FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_alarm_suppression_update_time BEFORE UPDATE ON alarm_suppression FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
