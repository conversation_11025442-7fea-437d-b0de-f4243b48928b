package com.geeksec.nta.alarm.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.vo.AlarmTargetAggVo;
import com.geeksec.nta.alarm.vo.AlarmTypeAggVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.vo.KnowledgeAlarmVo;

import java.util.List;
import java.util.Map;

/**
 * 告警数据仓库
 */
public interface AlarmService {


    /**
     * 告警：初始化告警&采集规则字典
     */
    void initKnowledgeType();

    /**
     * 告警：指标信息
     *
     * @param condition
     * @return
     */
    AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition);

    /**
     * 告警页面- 告警攻击链路展示
     * @param condition
     * @return
     */
    List<AlarmTypeAggVo.AttackChain> getModelAlarmAttackChainAggr(AlarmCommonCondition condition);


    /**
     * 告警知识库全量查询
     * @return
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList();

    /**
     * 获取告警列表（分类型）
     * @param condition
     * @return
     */
    PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition) throws JsonProcessingException;

    /**
     * 告警列表-告警详情
     * @param esIndex
     * @param alarmId
     * @return
     */
    Map<String, Object> getAlarmDetail2(String esIndex, String alarmId);

    /**
     * 更新告警文档状态
     * @param condition
     * @return
     */
    String updateDoc(AlarmStatusUpCondition condition) throws InterruptedException, JsonProcessingException;

    /**
     * 删除告警文档
     * @param condition
     * @return
     * @throws InterruptedException
     */
    Long deleteDoc(Map<Integer, List<String>> condition)  throws InterruptedException ;

    /**
     * 删除所有告警信息
     * @return
     */
    String deleteAllAlarm();

    /**
     * 获取告警导出列表PDF
     * @param condition
     * @return
     */
    String exportAlarmReport(AlarmListCondition condition);

    /**
     * 通过告警关联会话ID检索对应会话信息，生成到告警相关PCAP下载列表
     * @return
     */
    Map<String, Object> prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime);
}
