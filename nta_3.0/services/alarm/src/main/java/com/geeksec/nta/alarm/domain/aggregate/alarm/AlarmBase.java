package com.geeksec.nta.alarm.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.JacksonTypeHandler;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Table(value = "alarm_base", schema = "nta" , comment = "告警")
public class AlarmBase {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id ;

    @Column(value = "task_id" , comment = "任务id")
    private Long taskId ;

    @Column(value = "time" , comment = "告警时间")
    private Date time ;

    @Column(value = "alarm_name" , comment = "告警名称")
    private String alarmName ;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "targets", comment = "告警对象")
    private List<AlarmTargets> targets;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "victim", comment = "受害者")
    private List<AlarmVictim> victim;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @Column(typeHandler = JacksonTypeHandler.class, value = "attacker", comment = "攻击者")
    private List<AlarmAttacker> attacker;

    @Column(value = "attack_level" , comment = "威胁权重")
    private int attackLevel;

    @Column(value = "alarm_status" , comment = "处理状态")
    private int alarmStatus;

    @Column(value = "json" , comment = "告警具体数据")
    private String json;

    @Column(value = "alarm_knowledge_id" , comment = "告警名称id")
    private Long alarmKnowledgeId;

    @Column(value = "alarm_type" , comment = "告警类型")
    private String alarmType;

    @Column(value = "attack_chain_name" , comment = "攻击链名称")
    private List<String> attackChainName;

}