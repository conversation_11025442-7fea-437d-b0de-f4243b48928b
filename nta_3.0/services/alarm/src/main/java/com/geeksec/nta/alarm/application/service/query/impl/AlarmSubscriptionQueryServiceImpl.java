package com.geeksec.nta.alarm.application.service.query.impl;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.application.query.SubscriptionListQuery;
import com.geeksec.nta.alarm.application.service.query.AlarmSubscriptionQueryService;
import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription;
import com.geeksec.nta.alarm.domain.repository.AlarmSubscriptionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.domain.valueobject.UserId;
import com.geeksec.nta.alarm.infrastructure.converter.AlarmSubscriptionConverter;
import com.geeksec.nta.alarm.interfaces.dto.response.AlarmSubscriptionResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警订阅查询应用服务实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class AlarmSubscriptionQueryServiceImpl implements AlarmSubscriptionQueryService {

    private final AlarmSubscriptionRepository subscriptionRepository;
    private final AlarmSubscriptionConverter subscriptionConverter;

    @Override
    public PageResultVo<AlarmSubscriptionResponse> querySubscriptions(SubscriptionListQuery query) {
        log.info("分页查询用户订阅: {}", query);

        try {
            // 查询总数
            long total = subscriptionRepository.countByQuery(query);

            if (total == 0) {
                return PageResultVo.of(List.of(), 0L, query.getPageNum(), query.getPageSize());
            }

            // 查询分页数据
            List<AlarmSubscription> subscriptions = subscriptionRepository.findByQuery(query);
            List<AlarmSubscriptionResponse> responses = subscriptionConverter.toResponses(subscriptions);

            return PageResultVo.of(responses, total, query.getPageNum(), query.getPageSize());

        } catch (Exception e) {
            log.error("分页查询用户订阅失败: {}", query, e);
            throw new RuntimeException("查询订阅失败", e);
        }
    }

    @Override
    public Optional<AlarmSubscriptionResponse> getSubscription(SubscriptionId subscriptionId, UserId userId) {
        log.info("获取订阅详情: subscriptionId={}, userId={}", subscriptionId, userId);

        try {
            Optional<AlarmSubscription> subscriptionOpt = subscriptionRepository.findByIdAndUserId(subscriptionId,
                    userId);
            return subscriptionOpt.map(subscriptionConverter::toResponse);
        } catch (Exception e) {
            log.error("获取订阅详情失败: subscriptionId={}, userId={}", subscriptionId, userId, e);
            throw new RuntimeException("获取订阅详情失败", e);
        }
    }

    @Override
    public List<AlarmSubscriptionResponse> getUserSubscriptions(UserId userId) {
        log.info("获取用户的所有订阅: {}", userId);

        try {
            List<AlarmSubscription> subscriptions = subscriptionRepository.findByUserId(userId);
            return subscriptionConverter.toResponses(subscriptions);
        } catch (Exception e) {
            log.error("获取用户订阅失败: {}", userId, e);
            throw new RuntimeException("获取用户订阅失败", e);
        }
    }

    @Override
    public List<AlarmSubscriptionResponse> getUserActiveSubscriptions(UserId userId) {
        log.info("获取用户的活跃订阅: {}", userId);

        try {
            List<AlarmSubscription> subscriptions = subscriptionRepository.findActiveByUserId(userId);
            return subscriptionConverter.toResponses(subscriptions);
        } catch (Exception e) {
            log.error("获取用户活跃订阅失败: {}", userId, e);
            throw new RuntimeException("获取用户活跃订阅失败", e);
        }
    }

    @Override
    public boolean existsSubscription(SubscriptionId subscriptionId, UserId userId) {
        log.debug("检查订阅是否存在: subscriptionId={}, userId={}", subscriptionId, userId);

        try {
            return subscriptionRepository.existsByIdAndUserId(subscriptionId, userId);
        } catch (Exception e) {
            log.error("检查订阅是否存在失败: subscriptionId={}, userId={}", subscriptionId, userId, e);
            return false;
        }
    }

    @Override
    public boolean isSubscriptionNameUsed(String subscriptionName, UserId userId, SubscriptionId excludeId) {
        log.debug("检查订阅名称是否已被使用: name={}, userId={}, excludeId={}", subscriptionName, userId, excludeId);

        try {
            return subscriptionRepository.existsByNameAndUserIdExcludeId(subscriptionName, userId, excludeId);
        } catch (Exception e) {
            log.error("检查订阅名称是否已被使用失败: name={}, userId={}", subscriptionName, userId, e);
            return false;
        }
    }

    @Override
    public long countUserSubscriptions(UserId userId) {
        log.debug("统计用户订阅数量: {}", userId);

        try {
            return subscriptionRepository.countByUserId(userId);
        } catch (Exception e) {
            log.error("统计用户订阅数量失败: {}", userId, e);
            return 0;
        }
    }

    @Override
    public long countUserActiveSubscriptions(UserId userId) {
        log.debug("统计用户活跃订阅数量: {}", userId);

        try {
            return subscriptionRepository.countActiveByUserId(userId);
        } catch (Exception e) {
            log.error("统计用户活跃订阅数量失败: {}", userId, e);
            return 0;
        }
    }

    @Override
    public List<AlarmSubscriptionResponse> getAllActiveSubscriptions() {
        log.info("获取系统中所有活跃的订阅");

        try {
            List<AlarmSubscription> subscriptions = subscriptionRepository.findAllActive();
            return subscriptionConverter.toResponses(subscriptions);
        } catch (Exception e) {
            log.error("获取所有活跃订阅失败", e);
            throw new RuntimeException("获取所有活跃订阅失败", e);
        }
    }
}
