package com.geeksec.nta.alarm.interfaces.dto.request;

import com.geeksec.nta.alarm.application.query.SuppressionListQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 抑制规则查询请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "抑制规则查询请求")
public class SuppressionQueryRequest {
    
    @Schema(description = "页码", example = "1")
    @Builder.Default
    private Integer pageNum = 1;
    
    @Schema(description = "页大小", example = "20")
    @Builder.Default
    private Integer pageSize = 20;
    
    @Schema(description = "规则名称", example = "SQL注入抑制")
    private String ruleName;
    
    @Schema(description = "抑制类型", example = "IP_BASED")
    private String suppressionType;
    
    @Schema(description = "抑制动作", example = "SUPPRESS")
    private String action;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Schema(description = "最小优先级", example = "1")
    private Integer minPriority;
    
    @Schema(description = "最大优先级", example = "5")
    private Integer maxPriority;
    
    @Schema(description = "创建人", example = "admin")
    private String createdBy;
    
    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createTimeStart;
    
    @Schema(description = "创建时间结束", example = "2024-12-31T23:59:59")
    private LocalDateTime createTimeEnd;
    
    @Schema(description = "排序字段", example = "createTime")
    @Builder.Default
    private String sortField = "createTime";
    
    @Schema(description = "排序方向", example = "DESC")
    @Builder.Default
    private String sortDirection = "DESC";
    
    @Schema(description = "是否包含统计信息", example = "false")
    @Builder.Default
    private Boolean includeStats = false;
    
    /**
     * 转换为查询对象
     * 
     * @return 查询对象
     */
    public SuppressionListQuery toQuery() {
        return SuppressionListQuery.builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .ruleName(ruleName)
                .suppressionType(suppressionType)
                .action(action)
                .enabled(enabled)
                .minPriority(minPriority)
                .maxPriority(maxPriority)
                .createdBy(createdBy)
                .createTimeStart(createTimeStart)
                .createTimeEnd(createTimeEnd)
                .sortField(sortField)
                .sortDirection(sortDirection)
                .includeStats(includeStats)
                .build();
    }
}
