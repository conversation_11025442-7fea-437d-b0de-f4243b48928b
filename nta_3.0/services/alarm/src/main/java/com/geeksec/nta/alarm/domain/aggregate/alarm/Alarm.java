package com.geeksec.nta.alarm.domain.aggregate.alarm;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.geeksec.common.enums.AlarmHandlingStatus;
import com.geeksec.nta.alarm.domain.event.AlarmCreatedEvent;
import com.geeksec.nta.alarm.domain.event.AlarmStatusChangedEvent;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警聚合根
 * 负责告警的核心业务逻辑
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Data
@Table(value = "alarm", schema = "nta", comment = "告警")
public class Alarm {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id", comment = "id")
    private String id;

    @Column(value = "index", comment = "告警索引")
    private String index;

    @Column(value = "type", comment = "告警类型")
    private String type;

    @Column(value = "version", comment = "告警版本")
    private int version;

    @Column(value = "score", comment = "告警分数")
    private int score;

    @Column(value = "create_time", comment = "创建时间")
    private LocalDateTime createTime;

    @Column(value = "status", comment = "处理状态")
    private Integer status = AlarmHandlingStatus.UNHANDLED.getCode();

    @Column(value = "handler", comment = "处理人")
    private String handler;

    @Column(value = "handle_time", comment = "处理时间")
    private LocalDateTime handleTime;

    @Column(value = "handle_note", comment = "处理备注")
    private String handleNote;

    @Column(ignore = true)
    private AlarmSource source;

    @Column(ignore = true)
    private List<AlarmAttacker> attackers = new ArrayList<>();

    @Column(ignore = true)
    private List<AlarmVictim> victims = new ArrayList<>();

    @Column(ignore = true)
    private List<AlarmReason> reasons = new ArrayList<>();

    // 领域事件列表
    @Column(ignore = true)
    private final List<Object> domainEvents = new ArrayList<>();

    /**
     * 创建新告警
     */
    public static Alarm create(String index, String type, int score, AlarmSource source) {
        Alarm alarm = new Alarm();
        alarm.id = AlarmId.generate().getValue();
        alarm.index = index;
        alarm.type = type;
        alarm.score = score;
        alarm.source = source;
        alarm.createTime = LocalDateTime.now();
        alarm.status = AlarmHandlingStatus.UNHANDLED.getCode();

        // 发布告警创建事件
        alarm.addDomainEvent(new AlarmCreatedEvent(alarm.getId(), alarm.getType(), alarm.getScore()));

        log.info("创建新告警: id={}, type={}, score={}", alarm.id, type, score);
        return alarm;
    }

    /**
     * 确认告警
     */
    public void confirm(String handler, String note) {
        if (this.status.equals(AlarmHandlingStatus.CONFIRMED.getCode())) {
            log.warn("告警已经被确认: id={}", this.id);
            return;
        }

        AlarmHandlingStatus oldStatus = AlarmHandlingStatus.fromCode(this.status);
        this.status = AlarmHandlingStatus.CONFIRMED.getCode();
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleNote = note;

        // 发布状态变更事件
        addDomainEvent(new AlarmStatusChangedEvent(this.id, oldStatus, AlarmHandlingStatus.CONFIRMED, handler));

        log.info("确认告警: id={}, handler={}", this.id, handler);
    }

    /**
     * 标记为误报
     */
    public void markAsFalsePositive(String handler, String note) {
        if (this.status.equals(AlarmHandlingStatus.FALSE_POSITIVE.getCode())) {
            log.warn("告警已经被标记为误报: id={}", this.id);
            return;
        }

        AlarmHandlingStatus oldStatus = AlarmHandlingStatus.fromCode(this.status);
        this.status = AlarmHandlingStatus.FALSE_POSITIVE.getCode();
        this.handler = handler;
        this.handleTime = LocalDateTime.now();
        this.handleNote = note;

        // 发布状态变更事件
        addDomainEvent(new AlarmStatusChangedEvent(this.id, oldStatus, AlarmHandlingStatus.FALSE_POSITIVE, handler));

        log.info("标记告警为误报: id={}, handler={}", this.id, handler);
    }

    /**
     * 检查是否为高危告警
     */
    public boolean isHighRisk() {
        return this.score >= 80;
    }

    /**
     * 检查是否已处理
     */
    public boolean isHandled() {
        return !Objects.equals(AlarmHandlingStatus.UNHANDLED.getCode(), this.status);
    }

    /**
     * 获取告警ID值对象
     */
    public AlarmId getAlarmId() {
        return AlarmId.of(this.id);
    }

    /**
     * 添加攻击者
     */
    public void addAttacker(AlarmAttacker attacker) {
        if (attacker != null) {
            this.attackers.add(attacker);
        }
    }

    /**
     * 添加受害者
     */
    public void addVictim(AlarmVictim victim) {
        if (victim != null) {
            this.victims.add(victim);
        }
    }

    /**
     * 添加告警原因
     */
    public void addReason(AlarmReason reason) {
        if (reason != null) {
            this.reasons.add(reason);
        }
    }

    /**
     * 添加领域事件
     */
    private void addDomainEvent(Object event) {
        this.domainEvents.add(event);
    }

    /**
     * 获取并清空领域事件
     */
    public List<Object> getDomainEvents() {
        List<Object> events = new ArrayList<>(this.domainEvents);
        this.domainEvents.clear();
        return events;
    }
}