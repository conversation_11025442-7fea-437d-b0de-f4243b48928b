package com.geeksec.nta.alarm.interfaces.dto.request;

import com.geeksec.nta.alarm.application.command.UpdateSuppressionCommand;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 更新抑制规则请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "更新抑制规则请求")
public class UpdateSuppressionRequest {
    
    @Schema(description = "受害者IP", example = "*************")
    private String victim;
    
    @Schema(description = "攻击者IP", example = "********")
    private String attacker;
    
    @Schema(description = "告警标签", example = "SQL注入")
    private String label;
    
    @Schema(description = "抑制规则名称", example = "临时抑制SQL注入告警")
    private String suppressionName;
    
    @Schema(description = "抑制规则描述", example = "临时抑制来自内网的SQL注入告警")
    private String description;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Schema(description = "过期时间", example = "2024-12-31T23:59:59")
    private LocalDateTime expiryTime;
    
    @Schema(description = "备注", example = "更新抑制规则")
    private String note;
    
    /**
     * 转换为更新命令
     * 
     * @param suppressionId 抑制规则ID
     * @param operator 操作人
     * @return 更新命令
     */
    public UpdateSuppressionCommand toCommand(String suppressionId, String operator) {
        return UpdateSuppressionCommand.builder()
                .suppressionId(SuppressionId.of(suppressionId))
                .victim(victim)
                .attacker(attacker)
                .label(label)
                .suppressionName(suppressionName)
                .description(description)
                .enabled(enabled)
                .expiryTime(expiryTime)
                .operator(operator)
                .note(note)
                .build();
    }
}
