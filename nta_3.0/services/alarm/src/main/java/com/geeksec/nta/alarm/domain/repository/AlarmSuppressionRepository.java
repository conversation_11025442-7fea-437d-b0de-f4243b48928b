package com.geeksec.nta.alarm.domain.repository;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.common.entity.PageResultVo;

import java.util.List;
import java.util.Optional;

/**
 * 告警抑制规则聚合根仓储接口
 * 定义抑制规则聚合的持久化操作
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface AlarmSuppressionRepository {
    
    /**
     * 保存抑制规则
     * 
     * @param suppression 抑制规则聚合根
     * @return 保存后的抑制规则
     */
    AlarmSuppression save(AlarmSuppression suppression);
    
    /**
     * 根据ID查找抑制规则
     * 
     * @param suppressionId 抑制规则ID
     * @return 抑制规则聚合根
     */
    Optional<AlarmSuppression> findById(SuppressionId suppressionId);
    
    /**
     * 删除抑制规则
     * 
     * @param suppressionId 抑制规则ID
     * @return 是否删除成功
     */
    boolean deleteById(SuppressionId suppressionId);
    
    /**
     * 分页查询抑制规则
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageResultVo<AlarmSuppression> findByPage(SuppressionQuery query);
    
    /**
     * 查找活跃的抑制规则
     * 
     * @return 活跃的抑制规则列表
     */
    List<AlarmSuppression> findActiveRules();
    
    /**
     * 根据条件查找抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 匹配的抑制规则列表
     */
    List<AlarmSuppression> findByCondition(String victim, String attacker, String label);
    
    /**
     * 检查是否存在匹配的规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否存在匹配的规则
     */
    boolean existsMatchingRule(String victim, String attacker, String label);
    
    /**
     * 检查是否存在重复规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @param excludeId 排除的规则ID
     * @return 是否存在重复
     */
    boolean isDuplicateRule(String victim, String attacker, String label, SuppressionId excludeId);
    
    /**
     * 统计抑制规则数量
     * 
     * @return 总数量
     */
    long count();
    
    /**
     * 统计活跃抑制规则数量
     * 
     * @return 活跃数量
     */
    long countActive();
    
    /**
     * 根据条件批量删除
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 删除的数量
     */
    int deleteByCondition(String victim, String attacker, String label);
    
    /**
     * 获取规则最后更新时间
     *
     * @return 最后更新时间戳
     */
    long getLastUpdateTime();

    /**
     * 检查抑制规则名称是否存在
     *
     * @param suppressionName 抑制规则名称
     * @return 是否存在
     */
    boolean existsByName(String suppressionName);

    /**
     * 检查抑制规则名称是否存在（排除指定ID）
     *
     * @param suppressionName 抑制规则名称
     * @param excludeId 排除的规则ID
     * @return 是否存在
     */
    boolean existsByNameExcludeId(String suppressionName, SuppressionId excludeId);

    /**
     * 抑制规则查询条件
     */
    interface SuppressionQuery {
        // 查询条件接口，具体实现在application层
        int getPageNum();
        int getPageSize();
    }
}
