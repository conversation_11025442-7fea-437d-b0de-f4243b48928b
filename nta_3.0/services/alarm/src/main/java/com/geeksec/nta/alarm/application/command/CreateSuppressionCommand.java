package com.geeksec.nta.alarm.application.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 创建抑制规则命令
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSuppressionCommand {
    
    /**
     * 受害者IP
     */
    private String victim;
    
    /**
     * 攻击者IP
     */
    private String attacker;
    
    /**
     * 告警标签
     */
    private String label;
    
    /**
     * 抑制规则名称
     */
    private String suppressionName;
    
    /**
     * 抑制规则描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    @Builder.Default
    private Boolean enabled = true;
    
    /**
     * 过期时间（可选，null表示永不过期）
     */
    private LocalDateTime expiryTime;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 验证命令参数
     */
    public void validate() {
        if (victim == null || victim.trim().isEmpty()) {
            throw new IllegalArgumentException("受害者IP不能为空");
        }
        if (attacker == null || attacker.trim().isEmpty()) {
            throw new IllegalArgumentException("攻击者IP不能为空");
        }
        if (label == null || label.trim().isEmpty()) {
            throw new IllegalArgumentException("告警标签不能为空");
        }
        if (suppressionName == null || suppressionName.trim().isEmpty()) {
            throw new IllegalArgumentException("抑制规则名称不能为空");
        }
        if (creator == null || creator.trim().isEmpty()) {
            throw new IllegalArgumentException("创建者不能为空");
        }
        if (enabled == null) {
            throw new IllegalArgumentException("启用状态不能为空");
        }
        
        // 验证IP格式
        if (!isValidIpAddress(victim)) {
            throw new IllegalArgumentException("受害者IP格式不正确");
        }
        if (!isValidIpAddress(attacker)) {
            throw new IllegalArgumentException("攻击者IP格式不正确");
        }
        
        // 验证过期时间
        if (expiryTime != null && expiryTime.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("过期时间不能早于当前时间");
        }
    }
    
    /**
     * 简单的IP地址格式验证
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        // 支持通配符
        if ("*".equals(ip.trim())) {
            return true;
        }
        
        // 简单的IPv4格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        for (String part : parts) {
            if ("*".equals(part)) {
                continue;
            }
            try {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }
}
