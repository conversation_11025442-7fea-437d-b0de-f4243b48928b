package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.domain.aggregate.alarm.Alarm;
import com.geeksec.nta.alarm.domain.repository.AlarmRepository;
import com.geeksec.nta.alarm.domain.valueobject.AlarmId;
import com.geeksec.nta.alarm.infrastructure.mapper.AlarmMapper;
import com.geeksec.common.entity.PageResultVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 告警仓储实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AlarmRepositoryImpl implements AlarmRepository {
    
    private final AlarmMapper alarmMapper;
    
    @Override
    public Alarm save(Alarm alarm) {
        log.debug("保存告警: {}", alarm.getId());

        try {
            if (alarm.getId() == null) {
                // 新增
                alarm.setCreateTime(LocalDateTime.now());
                alarmMapper.insert(alarm);
                log.info("新增告警成功: id={}", alarm.getId());
            } else {
                // 更新
                alarmMapper.update(alarm);
                log.info("更新告警成功: id={}", alarm.getId());
            }
            return alarm;
        } catch (Exception e) {
            log.error("保存告警失败: {}", alarm.getId(), e);
            throw new RuntimeException("保存告警失败", e);
        }
    }
    
    @Override
    public Optional<Alarm> findById(AlarmId alarmId) {
        log.debug("根据ID查找告警: {}", alarmId.getValue());

        try {
            Alarm alarm = alarmMapper.selectOneById(alarmId.getValue());
            return Optional.ofNullable(alarm);
        } catch (Exception e) {
            log.error("根据ID查找告警失败: {}", alarmId.getValue(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Alarm> findByIndexAndId(String index, String alarmId) {
        log.debug("根据索引和ID查找告警: index={}, id={}", index, alarmId);

        try {
            // 这里需要根据具体的查询需求实现
            // 可能需要调用ES或其他数据源
            Alarm alarm = alarmMapper.selectOneById(alarmId);
            if (alarm != null && index.equals(alarm.getIndex())) {
                return Optional.of(alarm);
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("根据索引和ID查找告警失败: index={}, id={}", index, alarmId, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteById(AlarmId alarmId) {
        log.debug("删除告警: {}", alarmId.getValue());

        try {
            int deleted = alarmMapper.deleteById(alarmId.getValue());
            boolean success = deleted > 0;
            if (success) {
                log.info("删除告警成功: id={}", alarmId.getValue());
            } else {
                log.warn("告警不存在或删除失败: id={}", alarmId.getValue());
            }
            return success;
        } catch (Exception e) {
            log.error("删除告警失败: {}", alarmId.getValue(), e);
            throw new RuntimeException("删除告警失败", e);
        }
    }
    
    @Override
    public int batchDelete(List<AlarmId> alarmIds) {
        log.debug("批量删除告警: {}", alarmIds.size());

        try {
            List<String> ids = alarmIds.stream()
                    .map(AlarmId::getValue)
                    .collect(Collectors.toList());

            int deleted = alarmMapper.deleteBatchByIds(ids);
            log.info("批量删除告警成功: 删除数量={}", deleted);
            return deleted;
        } catch (Exception e) {
            log.error("批量删除告警失败", e);
            throw new RuntimeException("批量删除告警失败", e);
        }
    }

    @Override
    public PageResultVo<Alarm> findByPage(AlarmQuery query) {
        log.debug("分页查询告警");

        try {
            // 构建查询条件
            // 由于AlarmQuery是接口，这里需要根据具体实现来构建查询
            // 暂时返回空结果
            return PageResultVo.of(List.of(), 0L, 1, 20);
        } catch (Exception e) {
            log.error("分页查询告警失败", e);
            throw new RuntimeException("分页查询告警失败", e);
        }
    }

    @Override
    public long count(AlarmQuery query) {
        log.debug("统计告警数量");

        try {
            // 构建查询条件
            // 由于AlarmQuery是接口，这里需要根据具体实现来统计
            return alarmMapper.selectCountByQuery(null);
        } catch (Exception e) {
            log.error("统计告警数量失败", e);
            return 0;
        }
    }

    @Override
    public boolean existsById(AlarmId alarmId) {
        log.debug("检查告警是否存在: {}", alarmId.getValue());

        try {
            Alarm alarm = alarmMapper.selectOneById(alarmId.getValue());
            return alarm != null;
        } catch (Exception e) {
            log.error("检查告警是否存在失败: {}", alarmId.getValue(), e);
            return false;
        }
    }

    @Override
    public long deleteAll() {
        log.warn("删除所有告警");

        try {
            // 这是一个危险操作，需要谨慎使用
            long count = alarmMapper.selectCountByQuery(null);
            alarmMapper.truncateAlarms();
            log.warn("删除所有告警完成: 删除数量={}", count);
            return count;
        } catch (Exception e) {
            log.error("删除所有告警失败", e);
            throw new RuntimeException("删除所有告警失败", e);
        }
    }
}
