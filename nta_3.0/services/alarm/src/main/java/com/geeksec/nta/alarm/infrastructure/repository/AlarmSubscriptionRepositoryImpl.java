package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription;
import com.geeksec.nta.alarm.domain.repository.AlarmSubscriptionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SubscriptionId;
import com.geeksec.nta.alarm.domain.valueobject.UserId;
import com.geeksec.nta.alarm.infrastructure.mapper.AlarmSubscriptionMapper;
import com.geeksec.common.entity.PageResultVo;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static com.geeksec.nta.alarm.domain.aggregate.subscription.table.AlarmSubscriptionTableDef.ALARM_SUBSCRIPTION;

/**
 * 告警订阅仓储实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AlarmSubscriptionRepositoryImpl implements AlarmSubscriptionRepository {
    
    private final AlarmSubscriptionMapper subscriptionMapper;
    
    @Override
    public AlarmSubscription save(AlarmSubscription subscription) {
        log.debug("保存订阅: {}", subscription.getSubscriptionId());
        
        try {
            if (subscription.getId() == null) {
                // 新增
                subscription.setCreateTime(LocalDateTime.now());
                subscription.setUpdateTime(LocalDateTime.now());
                subscriptionMapper.insert(subscription);
                log.info("新增订阅成功: id={}", subscription.getId());
            } else {
                // 更新
                subscription.setUpdateTime(LocalDateTime.now());
                subscriptionMapper.update(subscription);
                log.info("更新订阅成功: id={}", subscription.getId());
            }
            return subscription;
        } catch (Exception e) {
            log.error("保存订阅失败: {}", subscription.getSubscriptionId(), e);
            throw new RuntimeException("保存订阅失败", e);
        }
    }
    
    @Override
    public Optional<AlarmSubscription> findById(SubscriptionId subscriptionId) {
        log.debug("根据ID查找订阅: {}", subscriptionId);
        
        try {
            AlarmSubscription subscription = subscriptionMapper.selectOneById(subscriptionId.getValue());
            return Optional.ofNullable(subscription);
        } catch (Exception e) {
            log.error("根据ID查找订阅失败: {}", subscriptionId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public Optional<AlarmSubscription> findByIdAndUserId(SubscriptionId subscriptionId, UserId userId) {
        log.debug("根据ID和用户ID查找订阅: subscriptionId={}, userId={}", subscriptionId, userId);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.ID.eq(subscriptionId.getValue()))
                    .and(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()));
            
            AlarmSubscription subscription = subscriptionMapper.selectOneByQuery(queryWrapper);
            return Optional.ofNullable(subscription);
        } catch (Exception e) {
            log.error("根据ID和用户ID查找订阅失败: subscriptionId={}, userId={}", subscriptionId, userId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public boolean deleteById(SubscriptionId subscriptionId) {
        log.debug("删除订阅: {}", subscriptionId);
        
        try {
            int deleted = subscriptionMapper.deleteById(subscriptionId.getValue());
            boolean success = deleted > 0;
            if (success) {
                log.info("删除订阅成功: id={}", subscriptionId);
            } else {
                log.warn("订阅不存在或删除失败: id={}", subscriptionId);
            }
            return success;
        } catch (Exception e) {
            log.error("删除订阅失败: {}", subscriptionId, e);
            throw new RuntimeException("删除订阅失败", e);
        }
    }
    
    @Override
    public PageResultVo<AlarmSubscription> findByUserIdAndPage(UserId userId, SubscriptionQuery query) {
        log.debug("分页查询用户订阅: userId={}", userId);
        
        try {
            // 构建查询条件
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()));
            
            // 添加其他查询条件
            buildQueryConditions(queryWrapper, query);
            
            // 分页查询
            Page<AlarmSubscription> page = Page.of(query.getPageNum(), query.getPageSize());
            Page<AlarmSubscription> result = subscriptionMapper.paginate(page, queryWrapper);
            
            return PageResultVo.of(result.getRecords(), result.getTotalRow(), 
                                 query.getPageNum(), query.getPageSize());
        } catch (Exception e) {
            log.error("分页查询用户订阅失败: userId={}", userId, e);
            throw new RuntimeException("分页查询用户订阅失败", e);
        }
    }
    
    @Override
    public List<AlarmSubscription> findByUserId(UserId userId) {
        log.debug("查找用户的所有订阅: userId={}", userId);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()))
                    .orderBy(ALARM_SUBSCRIPTION.CREATE_TIME.desc());
            
            List<AlarmSubscription> subscriptions = subscriptionMapper.selectListByQuery(queryWrapper);
            log.debug("用户{}有{}个订阅", userId, subscriptions.size());
            return subscriptions;
        } catch (Exception e) {
            log.error("查找用户订阅失败: userId={}", userId, e);
            throw new RuntimeException("查找用户订阅失败", e);
        }
    }
    
    @Override
    public List<AlarmSubscription> findActiveSubscriptions() {
        log.debug("查找活跃的订阅");
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.ENABLED.eq(true))
                    .orderBy(ALARM_SUBSCRIPTION.CREATE_TIME.desc());
            
            List<AlarmSubscription> subscriptions = subscriptionMapper.selectListByQuery(queryWrapper);
            log.debug("找到{}个活跃订阅", subscriptions.size());
            return subscriptions;
        } catch (Exception e) {
            log.error("查找活跃订阅失败", e);
            throw new RuntimeException("查找活跃订阅失败", e);
        }
    }
    
    @Override
    public boolean isSubscriptionNameUsed(String subscriptionName, UserId userId, SubscriptionId excludeId) {
        log.debug("检查订阅名称是否已被使用: name={}, userId={}, excludeId={}", 
                 subscriptionName, userId, excludeId);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.SUBSCRIPTION_NAME.eq(subscriptionName))
                    .and(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()));
            
            if (excludeId != null) {
                queryWrapper.and(ALARM_SUBSCRIPTION.ID.ne(excludeId.getValue()));
            }
            
            long count = subscriptionMapper.selectCountByQuery(queryWrapper);
            boolean used = count > 0;
            log.debug("订阅名称使用检查结果: {}", used);
            return used;
        } catch (Exception e) {
            log.error("检查订阅名称是否已被使用失败", e);
            return false;
        }
    }
    
    @Override
    public long countByUserId(UserId userId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()));
            
            return subscriptionMapper.selectCountByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("统计用户订阅数量失败: userId={}", userId, e);
            return 0;
        }
    }
    
    @Override
    public long countActiveByUserId(UserId userId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()))
                    .and(ALARM_SUBSCRIPTION.ENABLED.eq(true));
            
            return subscriptionMapper.selectCountByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("统计用户活跃订阅数量失败: userId={}", userId, e);
            return 0;
        }
    }
    
    @Override
    public boolean existsByIdAndUserId(SubscriptionId subscriptionId, UserId userId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUBSCRIPTION.ID.eq(subscriptionId.getValue()))
                    .and(ALARM_SUBSCRIPTION.USER_ID.eq(userId.getValue()));
            
            long count = subscriptionMapper.selectCountByQuery(queryWrapper);
            return count > 0;
        } catch (Exception e) {
            log.error("检查订阅是否存在失败: subscriptionId={}, userId={}", subscriptionId, userId, e);
            return false;
        }
    }
    
    /**
     * 构建查询条件
     */
    private void buildQueryConditions(QueryWrapper queryWrapper, SubscriptionQuery query) {
        // 这里需要根据具体的查询条件来构建
        // 由于SubscriptionQuery是接口，需要在application层实现具体的查询条件
        
        // 示例：如果有订阅名称搜索
        // if (StringUtils.hasText(query.getSubscriptionName())) {
        //     queryWrapper.and(ALARM_SUBSCRIPTION.SUBSCRIPTION_NAME.like(query.getSubscriptionName()));
        // }
        
        // 示例：如果有启用状态过滤
        // if (query.getEnabled() != null) {
        //     queryWrapper.and(ALARM_SUBSCRIPTION.ENABLED.eq(query.getEnabled()));
        // }
    }
}
