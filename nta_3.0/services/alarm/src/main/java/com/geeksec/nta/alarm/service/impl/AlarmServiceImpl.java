package com.geeksec.nta.alarm.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.geeksec.common.constants.Constants;
import com.geeksec.common.core.enums.ErrorCode;
import com.geeksec.common.core.exceptions.BusinessException;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmListCondition;
import com.geeksec.nta.alarm.dto.condition.AlarmStatusUpCondition;
import com.geeksec.nta.alarm.dto.condition.DownloadPcapCondition;
import com.geeksec.nta.alarm.entity.AlarmBase;
import com.geeksec.nta.alarm.entity.DownloadTask;
import com.geeksec.nta.alarm.mapper.AlarmMapper;
import com.geeksec.nta.alarm.mapper.KnowledgeAlarmMapper;
import com.geeksec.nta.alarm.service.AlarmService;
import com.geeksec.nta.alarm.vo.*;
import com.github.pagehelper.PageHelper;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.swing.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryWrapper.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class AlarmServiceImpl implements AlarmService {

    @Value("${send-url.alarm_report_export}")
    private String alarmReportExprotUrl;

    final KnowledgeAlarmMapper knowledgeAlarmMapper;

    final AlarmMapper alarmMapper;

    private static Map<Integer, KnowledgeTypeVo> alarnKnowledgeMap = new HashMap<>();

    @PostConstruct
    @Override
    public void initKnowledgeType() {
        List<KnowledgeTypeVo> knowledgeTypes = knowledgeAlarmMapper.getKnowledgeType();
        for (KnowledgeTypeVo knowledgeType : knowledgeTypes) {
            alarnKnowledgeMap.put(knowledgeType.getKnowledgeAlarmId(), knowledgeType);
        }
    }

    @Override
    public AlarmTargetAggVo getAlarmTargetAgg(AlarmCommonCondition condition) {
        return alarmMapper.getAlarmTargetAgg(condition);

    }

    private BusinessException checkParam(AlarmCommonCondition commonCondition) {
        List<Integer> taskIds = commonCondition.getTaskIds();
        if (taskIds == null || taskIds.size() < 1) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {
                    String[] split = attackLevel.split("-");
                    try {
                        Integer left = Integer.valueOf(split[0]);
                        Integer right = Integer.valueOf(split[1]);
                        if (left > right || left < 61 || right > 100) {
                            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
                        }
                    } catch (Exception e) {
                        throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
                    }
                }
            }
        }

        return null;
    }


    @Override
    public List<AlarmTypeAggVo.AttackChain> getModelAlarmAttackChainAggr(AlarmCommonCondition condition) {
        log.info("告警：模型告警攻击链聚合，condition={}", condition);
        List<Integer> taskIds = condition.getTaskIds();
        if (taskIds == null || taskIds.size() == 0) {
            return new ArrayList<>();
        }
        List<AlarmTypeAggVo.AttackChain> attackChainList = alarmMapper.getModelAlarmAttackChainAggr(condition);
        // 如果当前机器没有任何告警，则返回空
        if (attackChainList == null || attackChainList.size() < 1){
            return new ArrayList<>();
        }
        Map<Integer,String> alarmKnowledgeMap = knowledgeAlarmMapper.getAlarmKnowledgeMap();
        attackChainList.forEach(attackChain -> {
            List<AlarmTypeAggVo.AlarmKnowledge> alarmKnowledgeList = alarmMapper.getAlarmKnowledge(attackChain.getAlarmIds());
            alarmKnowledgeList.forEach(alarmKnowledge -> {
                alarmKnowledge.setAlarmKnowledgeName(alarmKnowledgeMap.get(alarmKnowledge.getAlarmKnowledgeId()));
            });
            attackChain.setAlarmKnowledgeList(alarmKnowledgeList);
        });
        return attackChainList;
    }


    @Override
    public List<KnowledgeAlarmVo> getKnowledgeAlarmList() {
        return knowledgeAlarmMapper.getKnowledgeAlarmList();
    }

    @Override
    public PageResultVo<Map<String, Object>> getAlarmList(AlarmListCondition condition) throws JsonProcessingException {
        log.info("告警：列表，condition={}", condition);
        String alarmType = condition.getAlarmType();
        //模型  防御   规则      注：  前3者是探针。   挖矿是   威胁情报 替换 防御
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }     //参数校验    1.时间 2.页码 3.每页条数
        BusinessException errorVo = checkParam(condition);
        if (errorVo != null) {
            throw  errorVo;
        }
        QueryWrapper wrapper = getCommonQueryBuilder(condition);
        List<Integer> taskIds = condition.getTaskIds();
        Integer pageSize= condition.getPageSize();
        Integer currentPage = condition.getCurrentPage();
        if (pageSize < 1) {
            //全部导出
            wrapper.orderBy("time", "desc");
        } else {
            PageHelper.startPage(currentPage, pageSize);
            String orderField = condition.getOrderField();
            Boolean asc = condition.getAsc();
            //设置排序
            if (StringUtils.isNotEmpty(orderField)) {
                if (asc) {
                    wrapper.orderBy(orderField, "asc");
                } else {
                    wrapper.orderBy(orderField, "desc");
                }
            } else {
                if (asc) {
                    wrapper.orderBy("time", "asc");
                } else {
                    wrapper.orderBy("time", "desc");
                }
            }
        }

        wrapper.eq("alarm_type", alarmType);
        //这里是前端勾选数据  不走查询条件
        List<String> ids = condition.getIds();
        if (CollectionUtil.isNotEmpty(ids)) {
            //id查询  清空上面的条件
            wrapper = new QueryWrapper();
            wrapper.in("id", ids);
            wrapper.in("task_id", taskIds);
        }
        List<AlarmBase> alarmBases = alarmMapper.selectListByQuery(wrapper);

        List<Map<String, Object>> list = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();

        for (AlarmBase alarmBase : alarmBases) {
            Map<String, Object> sourceMap = objectMapper.readValue(alarmBase.getJson(), new TypeReference<>() {});

            // 添加 _id 字段
            sourceMap.put("_id", alarmBase.getId());

            // 告警索引归属
            JsonNode jsonNode = objectMapper.readTree(alarmBase.getJson());
            String index = jsonNode.get("_index").toString();
            sourceMap.put("alarm_index", index);

            // 处理攻击类型字段
            int knowledgeId = Math.toIntExact(alarmBase.getAlarmKnowledgeId());
            KnowledgeTypeVo knowledgeTypeVo = alarnKnowledgeMap.get(knowledgeId);
            if (knowledgeTypeVo != null) {
                sourceMap.put("attack_type", knowledgeTypeVo.getAttackType());
                sourceMap.put("attack_type_name", knowledgeTypeVo.getAttackTypeName());
            } else {
                sourceMap.put("attack_type", 0);
                sourceMap.put("attack_type_name", "未知");
            }

            // 告警名称
            Object alarmName = sourceMap.get("alarm_name");
            if (alarmName == null) {
                /*String alarmNameValue;
                if (knowledgeTypeVo != null) {
                    alarmNameValue = knowledgeTypeVo.getAlarmName();
                } else {
                    // 对于规则告警中的告警ID，需要从标签和规则当中去查找对应的告警名称
                    TbTagInfo tag = tagInfoDao.getTagInfoById(knowledgeId);
                    FeatureRule rule = featureRuleDao.selectById(knowledgeId);
                    if (!ObjectUtils.isEmpty(tag)) {
                        alarmNameValue = tag.getTagText();
                    } else if (!ObjectUtils.isEmpty(rule)) {
                        alarmNameValue = rule.getRuleName();
                    } else {
                        alarmNameValue = "未知";
                    }
                }
                sourceMap.put("alarm_name", alarmNameValue);*/ //TODO 待处理
            }

            list.add(sourceMap);
        }

        // 构造会话ID列表
        List<String> sessionIdList = new ArrayList<>();
        if (alarmType.equals("模型")) {
            sessionIdList = list.stream()
                    .map(resultMap -> (List<String>) resultMap.getOrDefault("alarm_session_list", Collections.emptyList()))
                    .filter(sessionIds -> !sessionIds.isEmpty())
                    .map(sessionIds -> sessionIds.get(0))
                    .collect(Collectors.toList());
        } else if (alarmType.equals("规则")) {
            for (Map<String, Object> map : list) {
                Map<String, Object> sessionTarget = ((List<Map<String, Object>>) map.get("targets")).get(0);
                String sessionId = (String) sessionTarget.get("name");
                sessionIdList.add(sessionId);
            }
        }


        // 查询会话信息
        Map<String, Map<String, Object>> connectInfoMap = new HashMap<>();
        if (!sessionIdList.isEmpty()) {
            /*BoolQueryBuilder connectQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termsQuery("SessionId", sessionIdList));
            SearchSourceBuilder connectSourceBuilder = new SearchSourceBuilder().query(connectQueryBuilder);
            SearchRequest connectRequest = new SearchRequest("connectinfo_*").source(connectSourceBuilder);
            SearchResponse connectResponse = esearchService.esSearch(connectRequest);
            for (SearchHit hit : connectResponse.getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String sessionId = (String) sourceAsMap.get("SessionId");
                connectInfoMap.put(sessionId, hit.getSourceAsMap());
            }*/ //TODO 调用会话服务查询会话信息

            // 更新告警记录
            for (Map<String, Object> resultMap : list) {
                if (!resultMap.containsKey("alarm_session_list") && !resultMap.containsKey("targets")) {
                    continue;
                }

                String sessionId = null;
                if (alarmType.equals("模型")) {
                    sessionId = ((List<String>) resultMap.get("alarm_session_list")).get(0);
                } else if (alarmType.equals("规则")) {
                    Map<String, Object> sessionTarget = ((List<Map<String, Object>>) resultMap.get("targets")).get(0);
                    sessionId = (String) sessionTarget.get("name");
                }

                Map<String, Object> connectMap = connectInfoMap.get(sessionId);
                if (connectMap == null) {
                    resultMap.put("sIp", StringUtils.EMPTY);
                    resultMap.put("dIp", StringUtils.EMPTY);
                    resultMap.put("sPort", null);
                    resultMap.put("dPort", null);
                    resultMap.put("ProName", StringUtils.EMPTY);
                    continue;
                }

                resultMap.put("sIp", connectMap.getOrDefault("sIp", StringUtils.EMPTY));
                resultMap.put("dIp", connectMap.getOrDefault("dIp", StringUtils.EMPTY));
                resultMap.put("sPort", connectMap.get("sPort"));
                resultMap.put("dPort", connectMap.get("dPort"));
                resultMap.put("ProName", connectMap.get("ProName"));
            }
        }

        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setRecords(list);
        //pageResultVo.setTotal(searchResponse.getHits().totalHits); //TODO set会话服务返回条数
        return pageResultVo;
    }


    @Override
    public Map<String, Object> getAlarmDetail2(String esIndex, String alarmId) {
        AlarmBase alarmBase = alarmMapper.selectOneById(alarmId);
        if (ObjectUtils.isEmpty(alarmBase)) {
            return new HashMap<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map resultMap = objectMapper.convertValue(alarmBase.getJson(), Map.class);
        resultMap.put("_id", alarmId);
        Integer knowledgeId = Integer.parseInt(resultMap.get("alarm_knowledge_id").toString());
        KnowledgeTypeVo knowledgeTypeVo = alarnKnowledgeMap.get(knowledgeId);

        if (!ObjectUtils.isEmpty(knowledgeTypeVo)) {
            resultMap.put("attack_type", knowledgeTypeVo.getAttackType());
            resultMap.put("attack_type_name", knowledgeTypeVo.getAttackTypeName());
        } else {
            resultMap.put("attack_type", 0);
            resultMap.put("attack_type_name", "未知");
        }

        resultMap.put("es_index", esIndex);

        //告警名称
        Object alarmName = resultMap.get("alarm_name");
        if (alarmName == null) {
            if (knowledgeTypeVo != null) {
                resultMap.put("alarm_name", knowledgeTypeVo.getAlarmName());
            } else {
                // 对于规则告警中的告警ID，需要从标签和规则当中去查找对应的告警名称
                /*TbTagInfo tag = tagInfoDao.getTagInfoById(knowledgeId);
                FeatureRule rule = featureRuleDao.selectById(knowledgeId);
                if (!ObjectUtils.isEmpty(tag)) {
                    resultMap.put("alarm_name", tag.getTagText());
                } else if (!ObjectUtils.isEmpty(rule)) {
                    resultMap.put("alarm_name", rule.getRuleName());
                } else {
                    resultMap.put("alarm_name", "未知");
                }*/ //TODO 待处理
            }
        }

        if (!resultMap.containsKey("alarm_related_label")) {
            resultMap.put("alarm_related_label", new ArrayList<>());
        }

        return resultMap;
    }


    @Override
    public String updateDoc(AlarmStatusUpCondition condition) throws InterruptedException, JsonProcessingException {
        log.info("告警：修改文档状态，condition={}", condition);
        String id = condition.getId();
        Integer taskId = condition.getTaskId();
        Integer alarmStatus = condition.getAlarmStatus();
        if (StringUtils.isEmpty(id) || taskId == null || alarmStatus == null || alarmStatus < 0 || alarmStatus > 2) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("id", id);
        queryWrapper.eq("task_id", taskId);
        AlarmBase alarmBase = alarmMapper.selectOneByQuery(queryWrapper);
        if (ObjectUtils.isEmpty(alarmBase)) {
            log.error("告警：修改文档状态，查询数据为0");
            throw new BusinessException(ErrorCode.ES_UPDATE_ERROR);
        }
        alarmBase.setAlarmStatus(alarmStatus);
        String json = alarmBase.getJson();
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(json);
        ((ObjectNode)jsonNode.get("_source")).put("alarm_status",alarmStatus);
        alarmBase.setJson(objectMapper.writeValueAsString(jsonNode));
        int update = alarmMapper.update(alarmBase);
        if (update > 0) {
            if (condition.getAlarmStatus() == 2) {
                // 修改状态完成后，告警中存在这个"attack_chaim_list"这个key的话，需要进行数据库的录入
                Optional.ofNullable(jsonNode.get("attack_chain_list"))
                        .filter(JsonNode::isArray)
                        .map(node -> {
                            List<String> attackChainList = new ArrayList<>();
                            for (JsonNode element : node) {
                                String chain = element.asText(null);
                                if (chain != null) {
                                    String[] array = chain.split("_");
                                    if (array.length >= 3) {
                                        attackChainList.add(chain);
                                    }
                                }
                            }
                            return attackChainList;
                        })
                        .ifPresent(array -> {
                    String victimIp = array.get(0);
                    String attackerIp = array.get(1);
                    String labelStr = array.get(2);
                    long count = knowledgeAlarmMapper.countAttackChain(attackerIp, victimIp, labelStr);
                    if (count == 0) {
                        knowledgeAlarmMapper.insertAttackChain(attackerIp, victimIp, labelStr);
                    }
                });
            }
            return "修改告警状态成功";
        } else {
            throw new BusinessException(ErrorCode.ES_UPDATE_ERROR);
        }
    }

    @Override
    public Long deleteDoc(Map<Integer, List<String>> map) throws InterruptedException {
        if (map == null || map.size() < 1) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }
        int delete = 0;
        for (Integer taskId : map.keySet()) {
            List<String> ids = map.get(taskId);
            if (ids.size() != 0) {
                delete += alarmMapper.deleteBatchByIds(ids);
            }
        }
        if (delete != 0) {
            return (long) delete;
        } else {
            throw new BusinessException(ErrorCode.ES_DELETE_DOC_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteAllAlarm() {
        try {
            log.info("告警：删除所有告警信息");
            alarmMapper.deleteAll();
            return "删除所有告警成功";
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.ES_DELETE_DOC_ERROR);
        }
    }

    @Override
    public String exportAlarmReport(AlarmListCondition condition) {
        log.info("告警：列表，condition={}", condition);
        String alarmType = condition.getAlarmType();
        //模型  防御   规则      注：  前3者是探针。   挖矿是   威胁情报 替换 防御
        if (StringUtils.isEmpty(alarmType) || !("模型".equals(alarmType) || "防御".equals(alarmType) || "规则".equals(alarmType) || "威胁情报".equals(alarmType))) {
            throw new BusinessException(ErrorCode.REQUEST_PARAM_ERROR);
        }     //参数校验    1.时间 2.页码 3.每页条数
        BusinessException errorVo = checkParam(condition);
        if (errorVo != null) {
            throw  errorVo;
        }
        QueryWrapper wrapper = getCommonQueryBuilder(condition);
        List<Integer> taskIds = condition.getTaskIds();
        if (taskIds.size() == 1) {
            Integer taskId = taskIds.get(0);
            wrapper.eq("task_id", taskId);
        }

        // 筛选告警类型

        String orderField = condition.getOrderField();
        Boolean asc = condition.getAsc();
        //设置排序
        if (StringUtils.isNotEmpty(orderField)) {
            if (asc) {
                wrapper.orderBy(orderField, "asc");
            } else {
                wrapper.orderBy(orderField, "desc");
            }
        } else {
            if (asc) {
                wrapper.orderBy("time", "asc");
            } else {
                wrapper.orderBy("time", "desc");
            }
        }

        wrapper.eq("alarm_type", alarmType);
        //这里是前端勾选数据  不走查询条件
        List<String> ids = condition.getIds();
        if (ids != null && ids.size() > 0) {
            //id查询  清空上面的条件
            wrapper = new QueryWrapper();
            wrapper.in("id", ids);
            wrapper.in("task_id", taskIds);
        }

        try {
            /*String result = HttpRequest.post(alarmReportExprotUrl).body(searchSourceStr).timeout(60000).execute().body();
            log.info("生成告警检测报告PDF成功，返回结果：{}", result);
            JSONObject resultJson = JSONObject.parseObject(result);
            String code = resultJson.getString("code");
            String msg = resultJson.getString("ERROR_TEST_DATA");
            String filePath = resultJson.getString("data");
            if (!"200".equals(code)) {
                return msg;
            }
            return filePath;*/ //TODO PDF生成方式待完善
            return null;
        } catch (Exception e) {
            log.error("导入脆弱性检测任务，异常：{}", e);
            throw new BusinessException(ErrorCode.IMPORT_VULN_CHECK_TASK_FAIL);
        }

    }

    @Override
    public Map<String, Object> prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime) {
        log.info("通过告警关联会话ID，生成PCAP文件下载任务，sessionList={}, alarmType={}", alarmSessionList, alarmType);

        Map<String, Object> result = new HashMap<>();

        // 通过sessionID去查找会话元数据，先获取对应ES索引
        /*BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        //带入sessionId直接进行查询获取到元数据
        boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", alarmSessionList));
        SearchSourceBuilder sessionSourceBuilder = new SearchSourceBuilder();
        sessionSourceBuilder.query(boolQueryBuilder).size(alarmSessionList.size()).from(0);
        String[] includeFields = new String[]{"StartTime", "EndTime","SessionId","FirstProto","ThreadId"};
        SearchRequest sessionRequest = new SearchRequest("connectinfo*").source(sessionSourceBuilder);
        SearchResponse sessionResponse = esearchService.esSearch(sessionRequest);
        SearchHit[] sessionHits = sessionResponse.getHits().getHits();
        if (sessionHits.length == 0) {
            log.error("未找到对应的会话元数据，sessionList={}", alarmSessionList);
            return ResultVo.success("对应的会话元数据正在准备中，请稍后");
        }
        // 遍历查询到的所有session会话相关信息进行条件拼装组合
        List<DownloadPcapCondition.Session> pcapSessionList = new ArrayList<>();
        Set<Integer> taskIds = new HashSet<>();
        for(SearchHit sessionHit : sessionHits) {
            Map<String,Object> esMap = sessionHit.getSourceAsMap();

            DownloadPcapCondition.Session session = new DownloadPcapCondition.Session();
            session.setBatchId(sessionHit.getIndex().split("_")[2]);
            Long startTime = esMap.get("StartTime") == null ? null : Long.valueOf(esMap.get("StartTime").toString());
            session.setStartTime(startTime);
            Long endTime = esMap.get("EndTime") == null ? null : Long.valueOf(esMap.get("EndTime").toString());
            session.setEndTime(endTime);

            String sessionId = (String) esMap.get("SessionId");
            session.setFirstProto((Integer) esMap.get("FirstProto"));
            session.setSessionId(sessionId);

            // 任务ID为会话index第一个下划线后的第一个数字
            Integer taskId = Integer.parseInt(sessionHit.getIndex().split("_")[1]);;
            taskIds.add(taskId);
            session.setTaskId(taskId);
            session.setThreadId((Integer) esMap.get("ThreadId"));
            pcapSessionList.add(session);
        }

        // 组装show_query展示数据内容,固定与条件
        List<String> queryList = new ArrayList<>(alarmSessionList);
        Map<String, Map<String, Object>> showQuery = new HashMap<>();
        showQuery.put("and", new HashMap<String, Object>() {
            {
                put("SessionId", JSONArray.toJSON(queryList));
            }
        });
        showQuery.put("not", new HashMap<>());

        try {
            DownloadTask downloadTask = new DownloadTask();
            downloadTask.setCreatedTime(System.currentTimeMillis() / 1000);
            downloadTask.setUserId(userId);
            String queryStr = sessionSourceBuilder.query().toString();
            queryStr = queryStr.replaceAll("\\s+","");
            downloadTask.setQuery(queryStr);
            downloadTask.setType(0);
            downloadTask.setSessionId(JSONArray.toJSONString(pcapSessionList));
            downloadTask.setTaskId(taskIds.toString());
            downloadTask.setState(0);
            downloadTask.setStatus(1);
            downloadTask.setShowQuery(JSONObject.toJSONString(showQuery));
            downloadTaskDao.insert(downloadTask);
            result.put("id", downloadTask.getId());
        } catch (Exception e) {
            log.error("生成告警关联会话PCAP下载任务失败，异常：", e);
            throw new BusinessException(ErrorCode.ALARM_PCAP_DOWNLOAD_TASK_FAIL);
        }*/  //TODO 调用会话服务改写相关逻辑

        return result;
    }

    private QueryWrapper getCommonQueryBuilder(AlarmCommonCondition commonCondition) {
        QueryWrapper wrapper = new QueryWrapper();
        Long left = commonCondition.getLeft();
        Long right = commonCondition.getRight();
        if (left != null && left > 0) {
            //gt  大于    这里只用流量的开始时间~
            wrapper.ge("time", left);
        }
        if (right != null && right > 0) {
            wrapper.le("time", right);
        }

        //告警名称
        List<Integer> alarmIds = commonCondition.getAlarmIds();
        if (alarmIds != null && alarmIds.size() > 0) {
            wrapper.in("alarm_knowledge_id", alarmIds);
        }
        //taskId
        List<Integer> taskIds = commonCondition.getTaskIds();
        wrapper.in("task_id", taskIds);

        String targetName = commonCondition.getTargetName();
        if (StringUtils.isNotEmpty(targetName)) {
            wrapper.and("jsonb_path_exists(targets, '$[*] ? (@ == \""+targetName+"\")')");
        }

        //受害方 victim 下面的字段都查
        String victims = commonCondition.getVictim();
        if (StringUtils.isNotEmpty(victims)) {
            wrapper.and("jsonb_path_exists(victim, '$[*] ? (@ == \""+victims+"\")')");
        }
        //攻击方:ip
        String attackerIp = commonCondition.getAttackerIp();
        if (StringUtils.isNotEmpty(attackerIp)) {
            wrapper.and("jsonb_path_exists(attacker, '$[*] ? (@ == \""+attackerIp+"\")')");
        }
        //威胁等级
        List<String> attackLevels = commonCondition.getAttackLevels();
        if (attackLevels != null && attackLevels.size() > 0) {
            for (String attackLevel : attackLevels) {
                if (StringUtils.isNotEmpty(attackLevel)) {
                    String[] split = attackLevel.split("-");
                    wrapper.le("attack_level", split[1]);
                    wrapper.ge("attack_level", split[0]);
                }
            }
        }
        //处理状态
        List<Integer> alarmStatusList = commonCondition.getAlarmStatusList();
        if (alarmStatusList != null && alarmStatusList.size() > 0) {
            wrapper.in("alarm_status", alarmStatusList);
        }
        return wrapper;
    }
}
