package com.geeksec.nta.alarm.interfaces.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import com.geeksec.nta.alarm.domain.aggregate.subscription.AlarmSubscription.FrequencyType;
import com.geeksec.nta.alarm.interfaces.dto.request.FrequencyConfigDto;
import com.geeksec.nta.alarm.interfaces.dto.request.NotificationChannelDto;
import com.geeksec.nta.alarm.interfaces.dto.request.QuietHoursConfigDto;
import com.geeksec.nta.alarm.interfaces.dto.request.SubscriptionRuleDto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警订阅响应
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "告警订阅响应")
public class AlarmSubscriptionResponse {

    /**
     * 订阅ID
     */
    @Schema(description = "订阅ID", example = "sub-001")
    private String id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "user-001")
    private String userId;

    /**
     * 订阅名称
     */
    @Schema(description = "订阅名称", example = "高危告警订阅")
    private String subscriptionName;

    /**
     * 订阅描述
     */
    @Schema(description = "订阅描述", example = "订阅所有高危级别的告警")
    private String description;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "1")
    private Integer priorityLevel;

    /**
     * 匹配规则列表
     */
    @Schema(description = "匹配规则列表")
    private List<SubscriptionRuleDto> matchRules;

    /**
     * 通知渠道列表
     */
    @Schema(description = "通知渠道列表")
    private List<NotificationChannelDto> notificationChannels;

    /**
     * 通知频率类型
     */
    @Schema(description = "通知频率类型", example = "IMMEDIATE")
    private FrequencyType frequencyType;

    /**
     * 频率控制配置
     */
    @Schema(description = "频率控制配置")
    private FrequencyConfigDto frequencyConfig;

    /**
     * 是否启用免打扰
     */
    @Schema(description = "是否启用免打扰", example = "false")
    private Boolean quietHoursEnabled;

    /**
     * 免打扰时间配置
     */
    @Schema(description = "免打扰时间配置")
    private QuietHoursConfigDto quietHoursConfig;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 最后触发时间
     */
    @Schema(description = "最后触发时间")
    private LocalDateTime lastTriggeredTime;

    /**
     * 触发次数
     */
    @Schema(description = "触发次数", example = "10")
    private Long triggerCount;
}
