package com.geeksec.nta.alarm.interfaces.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订阅查询请求
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订阅查询请求")
public class SubscriptionQueryRequest {

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    @Builder.Default
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    @Schema(description = "页大小", example = "10")
    @Builder.Default
    private Integer pageSize = 10;

    /**
     * 订阅名称（模糊查询）
     */
    @Schema(description = "订阅名称", example = "高危告警")
    private String subscriptionName;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    /**
     * 优先级
     */
    @Schema(description = "优先级", example = "1")
    private Integer priorityLevel;

    /**
     * 转换为查询对象
     * 
     * @param userId 用户ID
     * @return 查询对象
     */
    public com.geeksec.nta.alarm.application.query.SubscriptionListQuery toQuery(String userId) {
        return com.geeksec.nta.alarm.application.query.SubscriptionListQuery.builder()
                .userId(userId)
                .subscriptionName(subscriptionName)
                .enabled(enabled)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
    }
}
