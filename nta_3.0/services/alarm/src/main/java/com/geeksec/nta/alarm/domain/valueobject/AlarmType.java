package com.geeksec.nta.alarm.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 告警类型值对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
@EqualsAndHashCode
public class AlarmType {

    /**
     * 告警类型值
     */
    private final String value;

    /**
     * 私有构造函数
     * 
     * @param value 告警类型值
     */
    private AlarmType(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("告警类型不能为空");
        }
        this.value = value.trim();
    }

    /**
     * 创建告警类型
     * 
     * @param value 告警类型值
     * @return 告警类型
     */
    public static AlarmType of(String value) {
        return new AlarmType(value);
    }

    /**
     * 检查是否为网络告警
     * 
     * @return 是否为网络告警
     */
    public boolean isNetworkAlarm() {
        return value.startsWith("NETWORK_");
    }

    /**
     * 检查是否为安全告警
     * 
     * @return 是否为安全告警
     */
    public boolean isSecurityAlarm() {
        return value.startsWith("SECURITY_");
    }

    /**
     * 检查是否为系统告警
     * 
     * @return 是否为系统告警
     */
    public boolean isSystemAlarm() {
        return value.startsWith("SYSTEM_");
    }

    @Override
    public String toString() {
        return value;
    }
}
