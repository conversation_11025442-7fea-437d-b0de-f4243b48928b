package com.geeksec.nta.alarm.infrastructure.repository;

import com.geeksec.nta.alarm.domain.aggregate.suppression.AlarmSuppression;
import com.geeksec.nta.alarm.domain.repository.AlarmSuppressionRepository;
import com.geeksec.nta.alarm.domain.valueobject.SuppressionId;
import com.geeksec.nta.alarm.infrastructure.converter.AlarmSuppressionConverter;
import com.geeksec.nta.alarm.infrastructure.mapper.AlarmSuppressionMapper;
import com.geeksec.common.entity.PageResultVo;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.geeksec.nta.alarm.domain.aggregate.suppression.table.AlarmSuppressionTableDef.ALARM_SUPPRESSION;

/**
 * 告警抑制规则仓储实现
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class AlarmSuppressionRepositoryImpl implements AlarmSuppressionRepository {
    
    private final AlarmSuppressionMapper suppressionMapper;
    private final AlarmSuppressionConverter suppressionConverter;
    
    @Override
    public AlarmSuppression save(AlarmSuppression suppression) {
        log.debug("保存抑制规则: {}", suppression.getSuppressionId());
        
        try {
            if (suppression.getId() == null) {
                // 新增
                suppressionMapper.insert(suppression);
                log.info("新增抑制规则成功: id={}", suppression.getId());
            } else {
                // 更新
                suppression.setUpdateTime(LocalDateTime.now());
                suppressionMapper.update(suppression);
                log.info("更新抑制规则成功: id={}", suppression.getId());
            }
            return suppression;
        } catch (Exception e) {
            log.error("保存抑制规则失败: {}", suppression.getSuppressionId(), e);
            throw new RuntimeException("保存抑制规则失败", e);
        }
    }
    
    @Override
    public Optional<AlarmSuppression> findById(SuppressionId suppressionId) {
        log.debug("根据ID查找抑制规则: {}", suppressionId);
        
        try {
            AlarmSuppression suppression = suppressionMapper.selectOneById(suppressionId.getValue());
            return Optional.ofNullable(suppression);
        } catch (Exception e) {
            log.error("根据ID查找抑制规则失败: {}", suppressionId, e);
            return Optional.empty();
        }
    }
    
    @Override
    public boolean deleteById(SuppressionId suppressionId) {
        log.debug("删除抑制规则: {}", suppressionId);
        
        try {
            int deleted = suppressionMapper.deleteById(suppressionId.getValue());
            boolean success = deleted > 0;
            if (success) {
                log.info("删除抑制规则成功: id={}", suppressionId);
            } else {
                log.warn("抑制规则不存在或删除失败: id={}", suppressionId);
            }
            return success;
        } catch (Exception e) {
            log.error("删除抑制规则失败: {}", suppressionId, e);
            throw new RuntimeException("删除抑制规则失败", e);
        }
    }
    
    @Override
    public PageResultVo<AlarmSuppression> findByPage(SuppressionQuery query) {
        log.debug("分页查询抑制规则");
        
        try {
            // 构建查询条件
            QueryWrapper queryWrapper = buildQueryWrapper(query);
            
            // 分页查询
            Page<AlarmSuppression> page = Page.of(query.getPageNum(), query.getPageSize());
            Page<AlarmSuppression> result = suppressionMapper.paginate(page, queryWrapper);
            
            return PageResultVo.of(result.getRecords(), result.getTotalRow(), 
                                 query.getPageNum(), query.getPageSize());
        } catch (Exception e) {
            log.error("分页查询抑制规则失败", e);
            throw new RuntimeException("分页查询抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppression> findActiveRules() {
        log.debug("查找活跃的抑制规则");
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.ENABLED.eq(true))
                    .and(ALARM_SUPPRESSION.EXPIRY_TIME.isNull()
                         .or(ALARM_SUPPRESSION.EXPIRY_TIME.gt(LocalDateTime.now())));
            
            List<AlarmSuppression> rules = suppressionMapper.selectListByQuery(queryWrapper);
            log.debug("找到{}个活跃的抑制规则", rules.size());
            return rules;
        } catch (Exception e) {
            log.error("查找活跃抑制规则失败", e);
            throw new RuntimeException("查找活跃抑制规则失败", e);
        }
    }
    
    @Override
    public List<AlarmSuppression> findByCondition(String victim, String attacker, String label) {
        log.debug("根据条件查找抑制规则: victim={}, attacker={}, label={}", victim, attacker, label);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create();
            
            if (StringUtils.hasText(victim)) {
                queryWrapper.and(ALARM_SUPPRESSION.VICTIM.eq(victim));
            }
            if (StringUtils.hasText(attacker)) {
                queryWrapper.and(ALARM_SUPPRESSION.ATTACKER.eq(attacker));
            }
            if (StringUtils.hasText(label)) {
                queryWrapper.and(ALARM_SUPPRESSION.LABEL.eq(label));
            }
            
            List<AlarmSuppression> rules = suppressionMapper.selectListByQuery(queryWrapper);
            log.debug("根据条件找到{}个抑制规则", rules.size());
            return rules;
        } catch (Exception e) {
            log.error("根据条件查找抑制规则失败", e);
            throw new RuntimeException("根据条件查找抑制规则失败", e);
        }
    }
    
    @Override
    public boolean existsMatchingRule(String victim, String attacker, String label) {
        log.debug("检查是否存在匹配的规则: victim={}, attacker={}, label={}", victim, attacker, label);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.ENABLED.eq(true))
                    .and(ALARM_SUPPRESSION.EXPIRY_TIME.isNull()
                         .or(ALARM_SUPPRESSION.EXPIRY_TIME.gt(LocalDateTime.now())))
                    .and(ALARM_SUPPRESSION.VICTIM.eq(victim).or(ALARM_SUPPRESSION.VICTIM.eq("*")))
                    .and(ALARM_SUPPRESSION.ATTACKER.eq(attacker).or(ALARM_SUPPRESSION.ATTACKER.eq("*")))
                    .and(ALARM_SUPPRESSION.LABEL.eq(label).or(ALARM_SUPPRESSION.LABEL.eq("*")));
            
            long count = suppressionMapper.selectCountByQuery(queryWrapper);
            boolean exists = count > 0;
            log.debug("匹配规则存在性检查结果: {}", exists);
            return exists;
        } catch (Exception e) {
            log.error("检查匹配规则失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isDuplicateRule(String victim, String attacker, String label, SuppressionId excludeId) {
        log.debug("检查是否存在重复规则: victim={}, attacker={}, label={}, excludeId={}", 
                 victim, attacker, label, excludeId);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.VICTIM.eq(victim))
                    .and(ALARM_SUPPRESSION.ATTACKER.eq(attacker))
                    .and(ALARM_SUPPRESSION.LABEL.eq(label));
            
            if (excludeId != null) {
                queryWrapper.and(ALARM_SUPPRESSION.ID.ne(excludeId.getValue()));
            }
            
            long count = suppressionMapper.selectCountByQuery(queryWrapper);
            boolean duplicate = count > 0;
            log.debug("重复规则检查结果: {}", duplicate);
            return duplicate;
        } catch (Exception e) {
            log.error("检查重复规则失败", e);
            return false;
        }
    }
    
    @Override
    public long count() {
        try {
            return suppressionMapper.selectCount();
        } catch (Exception e) {
            log.error("统计抑制规则总数失败", e);
            return 0;
        }
    }
    
    @Override
    public long countActive() {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.ENABLED.eq(true))
                    .and(ALARM_SUPPRESSION.EXPIRY_TIME.isNull()
                         .or(ALARM_SUPPRESSION.EXPIRY_TIME.gt(LocalDateTime.now())));
            
            return suppressionMapper.selectCountByQuery(queryWrapper);
        } catch (Exception e) {
            log.error("统计活跃抑制规则数量失败", e);
            return 0;
        }
    }
    
    @Override
    public int deleteByCondition(String victim, String attacker, String label) {
        log.debug("根据条件批量删除: victim={}, attacker={}, label={}", victim, attacker, label);
        
        try {
            QueryWrapper queryWrapper = QueryWrapper.create();
            
            if (StringUtils.hasText(victim)) {
                queryWrapper.and(ALARM_SUPPRESSION.VICTIM.eq(victim));
            }
            if (StringUtils.hasText(attacker)) {
                queryWrapper.and(ALARM_SUPPRESSION.ATTACKER.eq(attacker));
            }
            if (StringUtils.hasText(label)) {
                queryWrapper.and(ALARM_SUPPRESSION.LABEL.eq(label));
            }
            
            int deleted = suppressionMapper.deleteByQuery(queryWrapper);
            log.info("根据条件删除了{}个抑制规则", deleted);
            return deleted;
        } catch (Exception e) {
            log.error("根据条件批量删除失败", e);
            throw new RuntimeException("根据条件批量删除失败", e);
        }
    }
    
    @Override
    public long getLastUpdateTime() {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .select(ALARM_SUPPRESSION.UPDATE_TIME.max())
                    .from(ALARM_SUPPRESSION);
            
            LocalDateTime lastUpdateTime = suppressionMapper.selectObjectByQuery(queryWrapper);
            return lastUpdateTime != null ? 
                   lastUpdateTime.atZone(java.time.ZoneId.systemDefault()).toEpochSecond() : 0L;
        } catch (Exception e) {
            log.error("获取最后更新时间失败", e);
            return 0L;
        }
    }
    
    @Override
    public boolean existsByName(String suppressionName) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.SUPPRESSION_NAME.eq(suppressionName));
            
            long count = suppressionMapper.selectCountByQuery(queryWrapper);
            return count > 0;
        } catch (Exception e) {
            log.error("检查抑制规则名称是否存在失败: {}", suppressionName, e);
            return false;
        }
    }
    
    @Override
    public boolean existsByNameExcludeId(String suppressionName, SuppressionId excludeId) {
        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .where(ALARM_SUPPRESSION.SUPPRESSION_NAME.eq(suppressionName))
                    .and(ALARM_SUPPRESSION.ID.ne(excludeId.getValue()));
            
            long count = suppressionMapper.selectCountByQuery(queryWrapper);
            return count > 0;
        } catch (Exception e) {
            log.error("检查抑制规则名称是否存在失败: {}", suppressionName, e);
            return false;
        }
    }
    
    /**
     * 构建查询条件
     */
    private QueryWrapper buildQueryWrapper(SuppressionQuery query) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        // 这里需要根据具体的查询条件来构建
        // 由于SuppressionQuery是接口，需要在application层实现具体的查询条件
        
        return queryWrapper;
    }
}
