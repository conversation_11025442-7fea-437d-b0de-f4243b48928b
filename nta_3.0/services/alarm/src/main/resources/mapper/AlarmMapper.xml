<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.AlarmMapper">

    <resultMap id="BaseResultMap" type="com.geeksec.nta.alarm.entity.AlarmBase">
        <id column="id" property="id" />
        <id column="task_id" property="taskId" />
        <id column="time" property="time" />
        <id column="alarm_knowledge_id" property="alarmKnowledgeId" />
        <id column="targets" property="targets" />
        <id column="victim" property="victim" />
        <id column="attacker" property="attacker" />
        <id column="attack_level" property="attackLevel" />
        <id column="alarm_status" property="alarmStatus" />
        <id column="alarm_type" property="alarmType" />
        <id column="json" property="json" />
        <id column="alarm_name" property="alarmName" />
    </resultMap>

    <select id="getAlarmTargetAgg" resultType="com.geeksec.nta.alarm.vo.AlarmTargetAggVo">
        select
        count(1) as alarmCnt,
        sum(case when attack_level between 91 and 100 then 1 else 0 end) as highLevel,
        sum(case when attack_level between 81 and 90 then 1 else 0 end) as middleLevel,
        sum(case when attack_level between 60 and 80 then 1 else 0 end) as lowLevel,
        sum(case when alarm_status = 0 then 1 else 0 end) as alarmStatus0,
        sum(case when alarm_status = 1 then 1 else 0 end) as alarmStatus1,
        sum(case when alarm_status = 2 then 1 else 0 end) as alarmStatus2
        from alarm_base
        where 1 = 1
        <if test="condition.alarmIds != null">
            and alarm_knowledge_id in
            <foreach item="item" collection="condition.alarmIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="condition.taskIds != null">
            and task_id in
            <foreach item="item" collection="condition.taskIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="condition.targetName != null">
            and targets @@ to_tsquery('simple', #{condition.targetName})
        </if>
        <if test="condition.victim != null">
            and victims @@ to_tsquery('simple', #{condition.victim})
        </if>
        <if test="condition.attackerIp != null">
            and attacker @@ to_tsquery('simple', #{condition.attackerIp})
        </if>
        <if test="condition.attackLevels != null">
            and attack_level in
            <foreach item="item" collection="condition.attackLevels" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="condition.alarmStatusList != null">
            and alarm_status in
            <foreach item="item" collection="condition.alarmStatusList" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="condition.ids != null">
            and id in
            <foreach item="item" collection="condition.ids" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        <if test="condition.left != null">
            and time >= #{condition.left}
        </if>
        <if test="condition.right != null">
            and time lt;= #{condition.right}
        </if>

    </select>

    <select id="getModelAlarmAttackChainAggr" resultType="com.geeksec.nta.alarm.vo.AlarmTypeAggVo$AttackChain">
        select
        attack_chain_name as attackChainName,
        count(*) as attackChainCnt,
        ARRAY_AGG(id) AS alarmIds
        from alarm_base
        where
            alarm_type = '模型'
        <if test="condition.left != null">
            and time >= #{condition.left}
        </if>
        <if test="condition.right != null">
            and time lt;= #{condition.right}
        </if>
        <if test="condition.taskIds != null">
            and task_id in
            <foreach item="item" collection="condition.taskIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by attack_chain_name
    </select>

    <select id="getAlarmKnowledge" resultType="com.geeksec.nta.alarm.vo.AlarmTypeAggVo$AlarmKnowledge">
        select
            alarm_knowledge_id as alarmKnowledgeId,
            count(*) as alarmKnowledgeCnt
        from alarm_base
        where 1 = 1
        <if test="alarmIds != null">
            and id in
            <foreach item="item" collection="alarmIds" separator="," open="(" close=")" index="index">
                #{item}
            </foreach>
        </if>
        group by alarm_knowledge_id
    </select>

    <select id="deleteAll">
        truncate alarm_base
    </select>

</mapper>