server:
  port: 8090
  servlet:
    context-path: /api/alarm

spring:
  application:
    name: alarm-service

  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common

  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        namespace: ${KUBERNETES_NAMESPACE:nta}
      config:
        enabled: true
        sources:
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"

  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    dynamic:
      primary: nta-postgresql # 设置默认数据源
      datasource:
        nta-postgresql:
          url: ****************************************************************************************************************************
          username: postgres
          password: iGWrNGG47A
          driver-class-name: org.postgresql.Driver
        nta-mysql:
          url: jdbc:mysql://***************:23306/th_analysis?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
          username: root
          password: simpleuse23306p
          driver-class-name: com.mysql.cj.jdbc.Driver

    #type: com.alibaba.druid.pool.DruidDataSource
    #driver-class-name: org.postgresql.Driver
    #url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    #username: ${DB_USERNAME:nta_user}
    #password: ${DB_PASSWORD:nta_password}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

      # 监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

  # MyBatis Flex
  mybatis-flex:
    type-aliases-package: com.geeksec.nta.alarm.entity
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    global-config:
      print-banner: false
      key-config:
        key-type: auto

# 配置springdoc-openapi，用于文档化和访问API
springdoc:
  # 配置Swagger UI的访问路径和排序方式
  swagger-ui:
    path: /doc.html  # Swagger UI的访问路径
    tags-sorter: alpha      # 按字母顺序排序标签
    operations-sorter: alpha  # 按字母顺序排序操作
  # 配置API文档的访问路径
  api-docs:
    path: /v3/api-docs  # API文档的访问路径
  # 配置API分组，用于组织和管理API
  group-configs:
    - group: 'alarm'   # API分组名称
      paths-to-match: '/**'  # 匹配所有路径
      packages-to-scan: com.geeksec.nta.alarm.controller  # 扫描的包，用于自动发现API

# knife4j的增强配置
knife4j:
  enable: true
  setting:
    language: zh_cn

send-url:
  #告警报告导出
  alarm_report_export: http://***************:37777/probe_pdf_result

# NTA 告警订阅配置
nta:
  alarm:
    subscription:
      kafka:
        topic: subscription-changes
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
        producer:
          acks: 1
          retries: 3
          batch-size: 16384
          linger-ms: 1
    suppression:
      kafka:
        topic: alarm-suppression-changes
        bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}



